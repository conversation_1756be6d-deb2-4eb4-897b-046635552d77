<template>
  <view class="resume-container safe-area-inset-bottom">
    <scroll-view scroll-y enable-flex class="resume-content">
      <!-- 个人信息卡片 -->
      <view class="card personal-info">
        <view class="card-header" @click="editPersonalInfo">
          <view class="info-content flex-col">
            <view class="name-row flex-x-center">
              <view class="name">{{ resume.name }}</view>

              <view class="i-solar:pen-new-square-broken text-999"></view>
            </view>
            <view class="basic-info">
              <text class="info-text">{{ resume.gender }}</text>
              <text class="info-divider">·</text>
              <text class="info-text"
                >{{ calculateAge(resume.birthday) }}岁</text
              >
              <text class="info-divider">·</text>
              <text class="info-text">{{ resume.education }}</text>
            </view>
            <view class="contact-item flex-x-center gap-4rpx">
              <view class="i-carbon-phone text-999"></view>
              <view class="text-999">{{ formatPhoneNumber(resume.phone) }}</view>
            </view>
          </view>

          <view class="avatar-wrapper">
            <image
              class="avatar"
              src="/static/images/avatar-placeholder.png"
              mode="aspectFill"
            />
          </view>
        </view>

        <!-- <view class="info-bar">
          <view class="info-item">
            <text class="label">工作经验</text>
            <text class="value">{{ resume.experience }}</text>
          </view>
          <view class="info-item">
            <text class="label">学历</text>
            <text class="value">{{ resume.education }}</text>
          </view>
          <view class="info-item">
            <text class="label">年龄</text>
            <text class="value">{{ calculateAge(resume.birthday) }}岁</text>
          </view>
        </view> -->
      </view>

      <!-- 教育经历 -->
      <view class="card section-card">
        <view class="section-header">
          <view class="section-title">
            <text>教育经历</text>
          </view>
          <view class="section-edit" @tap="editEducation">
            <text class="i-carbon-edit"></text>
          </view>
        </view>

        <view class="section-content">
          <view
            v-for="(edu, index) in resume.educations"
            :key="index"
            :class="[
              'education-item',
              index < resume.educations.length - 1 ? 'with-border' : '',
            ]"
          >
            <view class="edu-header">
              <text class="school">{{ edu.school }}</text>
              <text class="time">{{ edu.time }}</text>
            </view>
            <view class="edu-details">
              <text class="major">{{ edu.major }}</text>
              <text class="degree">{{ edu.degree }}</text>
            </view>
          </view>

          <view class="add-item" @tap="addEducation">
            <text class="i-carbon-add"></text>
            <text>添加教育经历</text>
          </view>
        </view>
      </view>

      <!-- 工作经验 -->
      <view class="card section-card">
        <view class="section-header">
          <view class="section-title">
            <text>工作经验</text>
          </view>
          <view class="section-edit" @tap="editExperience">
            <text class="i-carbon-edit"></text>
          </view>
        </view>

        <view class="section-content">
          <view
            v-for="(exp, index) in resume.experiences"
            :key="index"
            class="work-experience"
          >
            <view class="experience-header">
              <view class="exp-title-row">
                <text class="company">{{ exp.company }}</text>
                <text class="time">{{ exp.time }}</text>
              </view>
              <view class="job-title">{{ exp.title }}</view>
            </view>

            <view class="responsibility">
              <view class="work-description">工作内容：</view>
              <view
                v-for="(item, idx) in exp.responsibilities"
                :key="idx"
                class="work-item"
              >
                <text class="dot">•</text>
                <text class="item-content">{{ item }}</text>
              </view>
            </view>

            <view
              class="divider"
              v-if="index < resume.experiences.length - 1"
            ></view>
          </view>

          <view class="add-item" @tap="addExperience">
            <text class="i-carbon-add"></text>
            <text>添加工作经验</text>
          </view>
        </view>
      </view>

      <!-- 专业技能 -->
      <view class="card section-card">
        <view class="section-header">
          <view class="section-title">
            <text>专业技能</text>
          </view>
          <view class="section-edit" @tap="editSkills">
            <text class="i-carbon-edit"></text>
          </view>
        </view>

        <view class="section-content">
          <view class="skills-container">
            <view
              v-for="(skill, index) in resume.skills"
              :key="index"
              class="skill-tag"
            >
              {{ skill }}
            </view>
          </view>

          <view class="skill-description">
            {{ resume.skillDescription }}
          </view>
        </view>
      </view>

      <!-- 自我评价 -->
      <view class="card section-card">
        <view class="section-header">
          <view class="section-title">
            <text>自我评价</text>
          </view>
          <view class="section-edit" @tap="editEvaluation">
            <text class="i-carbon-edit"></text>
          </view>
        </view>

        <view class="section-content">
          <view class="self-evaluation">
            {{ resume.selfEvaluation }}
          </view>
        </view>
      </view>

      <!-- 底部区域 -->
      <view class="bottom-area">
        <view class="update-time">简历更新于 {{ resume.updateTime }}</view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn outline-btn" @tap="downloadResume">
        <text class="i-carbon-download"></text>
        <text>下载简历</text>
      </view>
      <view class="action-btn primary-btn" @tap="editResume">
        <text class="i-carbon-edit"></text>
        <text>编辑简历</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formatPhoneNumber } from "@/utils";

// 简历数据
const resume = ref({
  name: "张小明",
  gender: "男",
  birthday: "1995-01-15", // 使用生日替代年龄
  phone: "13812345678",
  email: "<EMAIL>",
  jobIntent: "前端开发工程师",
  experience: "3年",
  education: "本科",

  // 教育经历
  educations: [
    {
      school: "浙江大学",
      major: "计算机科学与技术",
      degree: "本科",
      time: "2016.09-2020.07",
    },
    {
      school: "杭州市第一中学",
      major: "理科班",
      degree: "高中",
      time: "2013.09-2016.07",
    },
  ],

  // 工作经验
  experiences: [
    {
      company: "杭州某科技有限公司",
      title: "前端开发工程师",
      time: "2020.07-至今",
      responsibilities: [
        "负责公司电商平台的前端页面开发与维护",
        "参与项目需求分析，技术选型，架构设计",
        "基于uni-app开发跨平台小程序，提升用户体验",
        "优化前端性能，实现页面加载速度提升30%",
      ],
    },
    {
      company: "浙江某网络科技公司",
      title: "前端实习生",
      time: "2020.01-2020.06",
      responsibilities: [
        "参与公司官网和管理系统的开发",
        "使用Vue.js框架实现页面功能和交互效果",
        "配合后端完成接口对接，解决跨端适配问题",
      ],
    },
  ],

  // 技能
  skills: [
    "HTML/CSS",
    "JavaScript",
    "Vue.js",
    "uni-app",
    "TypeScript",
    "小程序开发",
    "Git",
  ],
  skillDescription:
    "熟练掌握前端开发技术栈，包括HTML/CSS/JavaScript，熟悉Vue.js框架及其生态，有丰富的小程序和H5开发经验，了解前端工程化和性能优化。",

  // 自我评价
  selfEvaluation:
    "性格开朗，责任心强，善于团队合作。具有良好的沟通能力和解决问题的能力，能够快速学习新技术并应用到实际项目中。热爱前端开发，有较强的代码洁癖，注重用户体验。",

  // 更新时间
  updateTime: "2023-10-15",
});

// 下载简历
const downloadResume = () => {
  uni.showToast({
    title: "简历已保存至手机",
    icon: "success",
  });
};

// 编辑简历
const editResume = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit",
  });
};

// 编辑头像
const editAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: () => {
      // 这里可以实现头像上传逻辑
      uni.showToast({
        title: "头像已更新",
        icon: "success",
      });
    },
  });
};

// 编辑个人信息
const editPersonalInfo = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=personal",
  });
};

// 编辑教育经历
const editEducation = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=education",
  });
};

// 添加教育经历
const addEducation = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=education&action=add",
  });
};

// 编辑工作经验
const editExperience = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=experience",
  });
};

// 添加工作经验
const addExperience = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=experience&action=add",
  });
};

// 编辑技能
const editSkills = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=skills",
  });
};

// 编辑自我评价
const editEvaluation = () => {
  uni.navigateTo({
    url: "/pages/job/resume-edit?section=evaluation",
  });
};

// 根据生日计算年龄
const calculateAge = (birthday: string): number => {
  if (!birthday) return 0;

  const birthDate = new Date(birthday);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // 如果生日还没过，年龄减1
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
};
</script>

<style lang="scss" scoped>
.resume-container {
  min-height: 100vh;
  width: 100%;
}

.resume-content {
  height: calc(100vh - 120rpx - env(safe-area-inset-bottom));
  width: 100%;
  box-sizing: border-box;
  // padding: $spacing-10;
}

// 卡片通用样式
.card {
  background-color: #fff;
  border-radius: $spacing-8;
  padding: $spacing-16 $spacing-12;
  margin-bottom: $spacing-10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
  margin: $spacing-10;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-16;
  padding-bottom: $spacing-10;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: $text-main;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: $spacing-8;
  border-left: 4rpx solid $primary;
}

.section-content {
  padding: $spacing-4 0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.section-edit {
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $text-info;
  transition: all 0.2s;

  &:active {
    background-color: #ebebeb;
    transform: scale(0.95);
    color: $primary;
  }
}

// 个人卡片特定样式
.personal-info {
  background: linear-gradient(to bottom, rgba($primary, 0.02), #fff);
}

.card-header {
  display: flex;
  position: relative;
  margin-bottom: $spacing-16;
  flex-wrap: wrap;
  justify-content: space-between;
}

// 头像样式
.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
  margin-left: $spacing-16;
  order: 2;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #f0f0f0;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 个人信息样式
.info-content {
  flex: 1;
  min-width: 0; // 防止内容溢出
  order: 1;

  .basic-info {
    font-size: 28rpx;
    color: $text-info;
    margin-bottom: $spacing-6;
  }
}

.name-row {
  margin-bottom: $spacing-8;
  flex-wrap: wrap;
}

.name {
  font-size: 40rpx;
  font-weight: 600;
  color: $text-main;
  margin-right: $spacing-10;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gender-age {
  font-size: 24rpx;
  color: $text-info;
  background-color: $bg-color;
  padding: 2rpx $spacing-8;
  border-radius: $spacing-10;
  white-space: nowrap;
}

.job-intent {
  font-size: 28rpx;
  color: $primary;
  font-weight: 500;
  margin-bottom: $spacing-8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: $text-info;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  text:first-child {
    margin-right: $spacing-4;
    flex-shrink: 0;
  }
}

// 信息栏样式
.info-bar {
  display: flex;
  justify-content: space-between;
  padding-top: $spacing-10;
  margin-top: $spacing-10;
  border-top: 1rpx solid $bg-color;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
  padding: $spacing-6;
  border-radius: $spacing-4;
  transition: all 0.2s;

  &:hover {
    background-color: rgba($primary, 0.03);
  }
}

.label {
  font-size: 24rpx;
  color: $text-info;
  margin-bottom: $spacing-4;
  white-space: nowrap;
}

.value {
  font-size: 26rpx;
  color: $text-main;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

// 教育经历样式
.education-item {
  padding: $spacing-10 0;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.with-border {
  border-bottom: 1rpx solid $bg-color;
}

.edu-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: $spacing-4;
  flex-wrap: wrap;
  width: 100%;
}

.edu-details {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
}

.school,
.company {
  font-size: 28rpx;
  font-weight: 500;
  color: $text-main;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.time {
  font-size: 24rpx;
  color: $text-info;
  white-space: nowrap;
}

.major,
.job-title {
  font-size: 26rpx;
  color: $text-info;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.degree {
  font-size: 24rpx;
  color: $primary;
  font-weight: 500;
  background-color: rgba($primary, 0.05);
  padding: 2rpx $spacing-6;
  border-radius: 4rpx;
  white-space: nowrap;
}

// 工作经验样式
.work-experience {
  position: relative;
  margin-bottom: $spacing-16;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.experience-header {
  padding-bottom: $spacing-4;
  width: 100%;
}

.exp-title-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: $spacing-4;
  flex-wrap: wrap;
  width: 100%;
}

.divider {
  height: 1rpx;
  background-color: $bg-color;
  margin: $spacing-16 0;
  width: 100%;
}

.responsibility {
  background-color: $bg-color;
  padding: $spacing-10;
  border-radius: $spacing-4;
  margin-top: $spacing-8;
  width: 100%;
  box-sizing: border-box;
}

.work-description {
  font-size: 24rpx;
  color: $text-info;
  margin-bottom: $spacing-4;
  font-weight: 500;
}

.work-item {
  display: flex;
  margin-bottom: $spacing-4;
  width: 100%;
}

.dot {
  font-size: 24rpx;
  color: $primary;
  margin-right: $spacing-4;
  flex-shrink: 0;
}

.item-content {
  font-size: 24rpx;
  color: $text-main;
  line-height: 1.5;
  word-break: break-all;
  flex: 1;
}

// 添加项按钮
.add-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  margin-top: 20rpx;
  border-radius: 8rpx;
  color: $primary;
  font-size: 28rpx;
  background-color: rgba($primary, 0.05);
  border: 1rpx dashed rgba($primary, 0.3);
  transition: all 0.2s;

  &:active {
    background-color: rgba($primary, 0.1);
    transform: scale(0.99);
  }

  text:first-child {
    margin-right: 6rpx;
  }
}

// 技能标签样式
.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-10;
  margin-bottom: $spacing-10;
  width: 100%;
  box-sizing: border-box;
}

.skill-tag {
  background-color: rgba($primary, 0.08);
  color: $primary;
  padding: $spacing-6 $spacing-12;
  border-radius: $spacing-16;
  font-size: 24rpx;
  transition: all 0.2s;
  max-width: 100%;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:active {
    background-color: rgba($primary, 0.12);
    transform: scale(0.98);
  }
}

.skill-description,
.self-evaluation {
  font-size: 26rpx;
  color: $text-main;
  line-height: 1.6;
  background-color: $bg-color;
  padding: $spacing-10;
  border-radius: $spacing-4;
  width: 100%;
  box-sizing: border-box;
  word-break: break-word;
}

// 底部区域
.bottom-area {
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
}

.update-time {
  font-size: 24rpx;
  color: $text-grey;
  background-color: #f0f0f0;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

// 底部按钮
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: $spacing-10;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: calc($spacing-10 + constant(safe-area-inset-bottom));
  padding-bottom: calc($spacing-10 + env(safe-area-inset-bottom));
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;

  text:first-child {
    margin-right: 10rpx;
  }

  &:active {
    transform: scale(0.98);
  }
}

.outline-btn {
  margin-right: 20rpx;
  border: 1rpx solid $primary;
  color: $primary;
  background-color: rgba($primary, 0.05);

  &:active {
    background-color: rgba($primary, 0.1);
  }
}

.primary-btn {
  background: linear-gradient(to right, $primary, $primary-500);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba($primary, 0.3);

  &:active {
    box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);
  }
}
</style>

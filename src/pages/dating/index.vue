<template>
  <view class="dating-page">
    <!-- 顶部导航栏 -->
    <view class="navbar-container sticky top-0 z-10">
      <view
        class="navbar px-30rpx py-20rpx flex justify-between items-center bg-white shadow-sm"
      >
        <view class="flex items-center">
          <view class="navbar-title text-36rpx font-bold">缘分交友</view>
          <view
            class="location-selector flex items-center ml-15rpx"
            @click="showCityPicker"
          >
            <text class="location-text text-26rpx">{{ currentCity }}</text>
            <text
              class="i-carbon-chevron-down ml-5rpx text-22rpx color-grey"
            ></text>
          </view>
        </view>
        <view class="navbar-actions flex items-center">
          <view
            class="action-icon mx-15rpx"
            @click="navigateTo('/pages/dating/search/index')"
          >
            <text class="i-carbon-search text-44rpx"></text>
          </view>
          <view
            class="action-icon mx-15rpx position-relative"
            @click="navigateTo('/pages/dating/chat/index')"
          >
            <text class="i-carbon-notification text-44rpx"></text>
            <view v-if="hasNewMessages" class="notification-badge"></view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view
      scroll-y
      class="dating-scroll"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing"
    >
      <!-- 广告轮播图 -->
      <view v-if="showBanner" class="banner-section px-30rpx py-20rpx">
        <swiper
          class="banner-swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="3000"
          :duration="500"
          indicator-color="rgba(255, 255, 255, 0.6)"
          indicator-active-color="#ff5778"
        >
          <swiper-item
            v-for="(item, index) in bannerList"
            :key="index"
            @click="onBannerClick(item)"
          >
            <image
              :src="item.image"
              mode="aspectFill"
              class="banner-image rounded-20rpx"
            />
            <view class="banner-overlay rounded-20rpx"></view>
            <view class="banner-content">
              <text class="banner-title">{{ item.title }}</text>
              <text class="banner-subtitle">{{ item.subtitle }}</text>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 简易筛选区 -->
      <view class="filter-section mx-30rpx my-20rpx">
        <view class="filter-tabs flex bg-white rounded-full shadow-sm">
          <view
            v-for="(tab, index) in genderTabs"
            :key="index"
            class="filter-tab flex-1 py-15rpx text-center rounded-full"
            :class="{ 'active-tab': activeGender === tab.value }"
            @click="setGender(tab.value)"
          >
            <text>{{ tab.label }}</text>
          </view>
        </view>
      </view>

      <!-- 功能导航 -->
      <view
        class="features-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm"
      >
        <view class="features-grid grid grid-cols-4 p-20rpx">
          <view
            v-for="(feature, index) in featureNavigations"
            :key="index"
            class="feature-item flex flex-col items-center py-10rpx"
            @click="navigateToFeature(feature.path)"
          >
            <view :class="`feature-icon-bg bg-${feature.color}-100 mb-10rpx`">
              <text
                :class="`i-carbon-${feature.icon} text-${feature.color}-500 text-44rpx`"
              ></text>
            </view>
            <text class="feature-name text-26rpx">{{ feature.name }}</text>
          </view>
        </view>
      </view>

      <!-- 缘分推荐 - 用户匹配 -->
      <view
        class="matching-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden"
      >
        <view
          class="section-header px-30rpx py-20rpx flex justify-between items-center"
        >
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">缘分推荐</text>
          </view>
          <view
            class="more-link flex items-center"
            @click="navigateTo('/pages/dating/search/index')"
          >
            <text class="text-26rpx color-primary">高级筛选</text>
            <text
              class="i-carbon-chevron-right color-primary text-24rpx"
            ></text>
          </view>
        </view>

        <view
          class="matched-users-grid grid grid-cols-2 gap-20rpx px-30rpx pb-30rpx"
        >
          <view
            v-for="user in matchedUsers.slice(0, 4)"
            :key="user.id"
            class="user-card bg-white rounded-15rpx overflow-hidden shadow-sm"
            @click="viewUserDetail(user.id)"
          >
            <view class="relative">
              <image :src="user.avatar" mode="aspectFill" class="user-avatar" />
              <view v-if="user.isVerified" class="verified-badge">
                <text class="i-carbon-checkmark mr-5rpx"></text>
                <text>已认证</text>
              </view>
            </view>
            <view class="user-info p-15rpx">
              <view class="flex justify-between items-center">
                <view class="flex items-center">
                  <text class="user-name text-30rpx font-bold">{{
                    user.name
                  }}</text>
                  <text class="user-age text-26rpx ml-10rpx"
                    >{{ user.age }}岁</text
                  >
                </view>
                <view
                  v-if="user.distance"
                  class="distance text-24rpx color-grey"
                >
                  {{ user.distance }}
                </view>
              </view>
              <view class="user-basic-info mt-10rpx flex justify-between">
                <text class="text-26rpx color-grey">{{ user.job }}</text>
                <text class="text-26rpx color-grey">{{ user.education }}</text>
              </view>
              <view class="user-tags flex flex-wrap mt-10rpx">
                <view
                  v-for="(tag, tagIndex) in user.tags.slice(0, 2)"
                  :key="tagIndex"
                  class="user-tag"
                >
                  {{ tag }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 本地相亲会 -->
      <view class="offline-events-section mx-30rpx my-20rpx">
        <view
          class="section-header flex justify-between items-center px-10rpx py-15rpx"
        >
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">本地相亲会</text>
          </view>
          <view
            class="more-link flex items-center"
            @click="navigateTo('/pages/dating/offline-events/index')"
          >
            <text class="text-26rpx color-primary">更多活动</text>
            <text
              class="i-carbon-chevron-right color-primary text-24rpx"
            ></text>
          </view>
        </view>

        <view class="events-list px-30rpx pb-30rpx">
          <view
            v-for="event in nearbyEvents"
            :key="event.id"
            class="event-card bg-white rounded-15rpx mb-20rpx shadow-sm overflow-hidden"
            @click="viewEventDetail(event.id)"
          >
            <image :src="event.image" class="event-image" mode="aspectFill" />
            <view class="event-info p-20rpx">
              <view class="flex justify-between items-start">
                <text class="event-title text-32rpx font-bold line-clamp-1">{{
                  event.title
                }}</text>
                <view
                  class="event-status"
                  :class="{ 'status-active': !event.isFull }"
                >
                  {{ event.isFull ? "名额已满" : "报名中" }}
                </view>
              </view>
              <view class="event-meta mt-15rpx">
                <view class="flex items-center mt-10rpx">
                  <text
                    class="i-carbon-calendar color-grey text-28rpx mr-10rpx"
                  ></text>
                  <text class="event-time text-26rpx color-grey">{{
                    event.time
                  }}</text>
                </view>
                <view class="flex items-center mt-10rpx">
                  <text
                    class="i-carbon-location color-grey text-28rpx mr-10rpx"
                  ></text>
                  <text
                    class="event-address text-26rpx color-grey line-clamp-1"
                    >{{ event.address }}</text
                  >
                </view>
              </view>
              <view
                class="event-footer flex justify-between items-center mt-15rpx"
              >
                <view class="gender-ratio flex items-center">
                  <view class="male-ratio">
                    <text class="gender-label text-24rpx mr-5rpx">男</text>
                    <text class="gender-count text-24rpx color-grey"
                      >{{ event.maleCount }}/{{ event.maleLimit }}</text
                    >
                  </view>
                  <view class="female-ratio ml-20rpx">
                    <text class="gender-label text-24rpx mr-5rpx">女</text>
                    <text class="gender-count text-24rpx color-grey"
                      >{{ event.femaleCount }}/{{ event.femaleLimit }}</text
                    >
                  </view>
                </view>
                <view class="flex items-center">
                  <text class="event-price text-primary text-34rpx font-bold"
                    >¥{{ event.price }}</text
                  >
                  <view
                    class="signup-btn ml-20rpx"
                    :class="{ 'btn-disabled': event.isFull }"
                  >
                    {{ event.isFull ? "已满额" : "报名" }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 本地红娘 -->
      <view class="matchmaker-section mx-30rpx my-20rpx">
        <view
          class="section-header flex justify-between items-center px-10rpx py-15rpx"
        >
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">本地红娘</text>
          </view>
          <view
            class="more-link flex items-center"
            @click="navigateTo('/pages/dating/matchmakers/index')"
          >
            <text class="text-26rpx color-primary">查看全部</text>
            <text
              class="i-carbon-chevron-right color-primary text-24rpx"
            ></text>
          </view>
        </view>

        <!-- 本地红娘展示 -->
        <view
          class="matchmakers-grid grid grid-cols-2 gap-20rpx px-30rpx pb-30rpx"
        >
          <view
            v-for="matchmaker in localMatchmakers.slice(0, 4)"
            :key="matchmaker.id"
            class="matchmaker-card bg-white rounded-15rpx overflow-hidden shadow-sm"
            @click="viewMatchmakerDetail(matchmaker.id)"
          >
            <view class="relative">
              <image
                :src="matchmaker.avatar"
                mode="aspectFill"
                class="matchmaker-avatar"
              />
              <view class="matchmaker-stats bg-primary-gradient">
                <text class="text-24rpx text-white"
                  >成功配对: {{ matchmaker.successCount }}</text
                >
              </view>
            </view>
            <view class="matchmaker-info p-15rpx">
              <view class="flex justify-between items-center">
                <view class="flex items-center">
                  <text class="matchmaker-name text-30rpx font-bold">{{
                    matchmaker.name
                  }}</text>
                  <view
                    v-if="matchmaker.isOfficial"
                    class="official-badge ml-10rpx"
                  >
                    <text class="text-22rpx">官方</text>
                  </view>
                </view>
                <view class="matchmaker-rating">
                  <text class="text-24rpx">★ {{ matchmaker.rating }}</text>
                </view>
              </view>
              <view class="matchmaker-experience mt-10rpx">
                <text class="text-26rpx color-grey"
                  >{{ matchmaker.years }}年经验 | {{ matchmaker.area }}</text
                >
              </view>
              <view class="matchmaker-services flex flex-wrap mt-10rpx">
                <view
                  v-for="(service, sIndex) in matchmaker.services.slice(0, 2)"
                  :key="sIndex"
                  class="service-tag"
                >
                  {{ service }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 成功故事 -->
      <view
        class="success-stories-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden"
      >
        <view
          class="section-header px-30rpx py-20rpx flex justify-between items-center"
        >
          <view class="section-title flex items-center">
            <view class="title-indicator"></view>
            <text class="text-34rpx font-bold">成功故事</text>
          </view>
          <view
            class="more-link flex items-center"
            @click="navigateTo('/pages/dating/success-stories/index')"
          >
            <text class="text-26rpx color-primary">查看全部</text>
            <text
              class="i-carbon-chevron-right color-primary text-24rpx"
            ></text>
          </view>
        </view>

        <view class="stories-scroll hide-scrollbar px-30rpx pb-30rpx">
          <view class="stories-list">
            <view
              v-for="(story, index) in successStories"
              :key="story.id"
              class="story-card bg-white rounded-15rpx mb-20rpx overflow-hidden shadow-sm"
              @click="viewSuccessStory(story.id)"
            >
              <view class="story-header p-20rpx flex items-center">
                <view class="couple-avatars relative mr-20rpx">
                  <image
                    :src="story.maleAvatar"
                    class="male-avatar"
                    mode="aspectFill"
                  />
                  <image
                    :src="story.femaleAvatar"
                    class="female-avatar"
                    mode="aspectFill"
                  />
                  <view class="heart-icon bg-primary">
                    <text class="i-carbon-favorite-filled text-white"></text>
                  </view>
                </view>
                <view class="story-title">
                  <text class="story-names text-32rpx font-bold">{{
                    story.names
                  }}</text>
                  <view class="story-meta flex items-center mt-10rpx">
                    <text class="text-26rpx color-grey">{{ story.date }}</text>
                    <text class="dot-separator mx-10rpx">·</text>
                    <text class="text-26rpx color-grey">{{
                      story.location
                    }}</text>
                  </view>
                </view>
              </view>
              <view class="story-content p-20rpx">
                <text class="story-desc text-28rpx line-clamp-2">{{
                  story.description
                }}</text>
              </view>
              <view
                class="story-footer p-20rpx flex justify-between items-center border-top"
              >
                <view class="matchmaker-info flex items-center">
                  <image
                    :src="story.matchmakerAvatar"
                    class="matchmaker-avatar-sm"
                    mode="aspectFill"
                  />
                  <text class="matchmaker-name text-26rpx ml-10rpx">{{
                    story.matchmakerName
                  }}</text>
                </view>
                <view class="story-stats flex items-center">
                  <text class="i-carbon-view mr-5rpx"></text>
                  <text class="text-26rpx color-grey">{{ story.views }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 约会攻略 -->
        <view
          class="dating-guides-section bg-white rounded-20rpx mx-30rpx my-20rpx shadow-sm overflow-hidden"
        >
          <view
            class="section-header px-30rpx py-20rpx flex justify-between items-center"
          >
            <view class="section-title flex items-center">
              <view class="title-indicator"></view>
              <text class="text-34rpx font-bold">约会攻略</text>
            </view>
            <view
              class="more-link flex items-center"
              @click="navigateTo('/pages/dating/guides/index')"
            >
              <text class="text-26rpx color-primary">更多攻略</text>
              <text
                class="i-carbon-chevron-right color-primary text-24rpx"
              ></text>
            </view>
          </view>

          <view class="guides-list px-30rpx pb-30rpx">
            <view
              v-for="(guide, index) in datingGuides"
              :key="index"
              class="guide-item flex p-20rpx mb-20rpx bg-white rounded-15rpx shadow-sm"
              @click="viewGuideDetail(guide.id)"
            >
              <image
                :src="guide.image"
                mode="aspectFill"
                class="guide-image rounded-10rpx"
              />
              <view class="guide-info flex-1 ml-20rpx">
                <text class="guide-title text-32rpx font-bold line-clamp-2">{{
                  guide.title
                }}</text>
                <text
                  class="guide-desc text-26rpx color-grey line-clamp-2 mt-10rpx"
                  >{{ guide.description }}</text
                >
                <view class="flex justify-between items-center mt-15rpx">
                  <view class="flex items-center">
                    <image
                      :src="guide.authorAvatar"
                      class="author-avatar"
                      mode="aspectFill"
                    />
                    <text class="author text-24rpx color-grey ml-10rpx">{{
                      guide.author
                    }}</text>
                  </view>
                  <view class="flex items-center">
                    <text class="i-carbon-thumbs-up color-grey mr-5rpx"></text>
                    <text class="like-count text-24rpx color-grey">{{
                      guide.likeCount
                    }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 使用 tui-tabbar 替换原有底部导航 -->
    <tui-tabbar
      :current="currentTabIndex"
      :tabBar="tabBarList"
      :isFixed="true"
      :backgroundColor="'#ffffff'"
      :selectedColor="'#ff5778'"
      :color="'#999999'"
      @click="onTabBarClick"
    ></tui-tabbar>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 下拉刷新状态
const refreshing = ref(false);
const onRefresh = () => {
  // 模拟刷新数据
  setTimeout(() => {
    refreshing.value = false;
  }, 1500);
};

// Banner轮播图数据
const bannerList = ref([
  {
    id: 1,
    image: "https://picsum.photos/700/300?random=1",
    title: "缘分良缘·高端相亲",
    subtitle: "百万精英等你来撩",
    link: "/pages/dating/activities/id=10001",
  },
  {
    id: 2,
    image: "https://picsum.photos/700/300?random=2",
    title: "七夕脱单派对",
    subtitle: "一对一心动匹配",
    link: "/pages/dating/activities/id=10002",
  },
  {
    id: 3,
    image: "https://picsum.photos/700/300?random=3",
    title: "恋爱学堂",
    subtitle: "专业老师教你脱单技巧",
    link: "/pages/dating/activities/id=10003",
  },
]);

// 功能导航
const featureNavigations = ref([
  {
    name: "速配约会",
    icon: "lightning",
    color: "red",
    path: "/pages/dating/quick-match",
  },
  {
    name: "线下活动",
    icon: "calendar",
    color: "blue",
    path: "/pages/dating/offline-events",
  },
  {
    name: "心理测试",
    icon: "chart-evaluation",
    color: "purple",
    path: "/pages/dating/psychological-test",
  },
  {
    name: "恋爱学堂",
    icon: "education",
    color: "green",
    path: "/pages/dating/love-academy",
  },
]);

// 精选活动
const datingActivities = ref([
  {
    id: "1001",
    image: "https://picsum.photos/350/200?random=4",
    title: "周末相约·高质量单身交友派对",
    time: "本周六 14:00-17:00",
    location: "北京市朝阳区三里屯SOHO",
    type: "线下",
    price: "168",
    originalPrice: "298",
    currentCount: 32,
    maxCount: 50,
    isHot: true,
    isFull: false,
  },
  {
    id: "1002",
    image: "https://picsum.photos/350/200?random=5",
    title: "户外徒步·遇见心动的TA",
    time: "下周日 09:00-16:00",
    location: "北京市海淀区凤凰岭景区",
    type: "户外",
    price: "128",
    originalPrice: "198",
    currentCount: 28,
    maxCount: 40,
    isHot: false,
    isFull: false,
  },
  {
    id: "1003",
    image: "https://picsum.photos/350/200?random=6",
    title: "烘焙甜点·甜蜜邂逅",
    time: "本周日 13:30-16:30",
    location: "北京市西城区烘焙工作室",
    type: "室内",
    price: "198",
    originalPrice: "268",
    currentCount: 18,
    maxCount: 24,
    isHot: false,
    isFull: false,
  },
  {
    id: "1004",
    image: "https://picsum.photos/350/200?random=7",
    title: "高端酒会·精英约会",
    time: "下周五 19:30-22:00",
    location: "北京市朝阳区W酒店",
    type: "高端",
    price: "298",
    originalPrice: "498",
    currentCount: 40,
    maxCount: 40,
    isHot: false,
    isFull: true,
  },
]);

// 用户筛选标签
const filterTabs = ref([
  { label: "全部", value: "all" },
  { label: "同城", value: "nearby" },
  { label: "已认证", value: "verified" },
  { label: "新人", value: "new" },
]);

const activeTab = ref("all");
const setActiveTab = (tab: string) => {
  activeTab.value = tab;
};

const currentCardIndex = ref(0);
const onSwiperChange = (e: any) => {
  currentCardIndex.value = e.detail.current;
};

// 推荐用户数据
const recommendUsers = ref([
  {
    id: "2001",
    avatar: "https://picsum.photos/400/600?random=8",
    name: "小林",
    age: 28,
    gender: "male",
    job: "产品经理",
    education: "硕士",
    tags: ["旅行", "摄影", "美食"],
    location: "朝阳区·2.5km",
    isVip: true,
    isVerified: true,
    isLiked: false,
    introduction:
      "热爱生活，喜欢尝试新鲜事物。工作认真负责，生活中是个有趣的人。希望找一个真诚、有共同话题的女生。",
  },
  {
    id: "2002",
    avatar: "https://picsum.photos/400/600?random=9",
    name: "安妮",
    age: 26,
    gender: "female",
    job: "财务",
    education: "本科",
    tags: ["电影", "音乐", "健身"],
    location: "海淀区·3.8km",
    isVip: false,
    isVerified: true,
    isLiked: true,
    introduction:
      "性格开朗，喜欢音乐和电影。平时喜欢健身和阅读，希望找一个有上进心、成熟稳重的男生共度余生。",
  },
  {
    id: "2003",
    avatar: "https://picsum.photos/400/600?random=10",
    name: "小王",
    age: 29,
    gender: "male",
    job: "设计师",
    education: "本科",
    tags: ["插画", "动漫", "宅家"],
    location: "西城区·5.2km",
    isVip: true,
    isVerified: false,
    isLiked: false,
    introduction:
      "性格内向但不社恐，喜欢宅在家里画画和看动漫。希望找一个能理解我的生活方式，同时也能一起成长的女生。",
  },
  {
    id: "2004",
    avatar: "https://picsum.photos/400/600?random=11",
    name: "小美",
    age: 25,
    gender: "female",
    job: "教师",
    education: "本科",
    tags: ["阅读", "瑜伽", "烘焙"],
    location: "东城区·4.1km",
    isVip: false,
    isVerified: true,
    isLiked: false,
    introduction:
      "教师，热爱教育事业。闲暇时喜欢做瑜伽和烘焙。希望找一个稳重、顾家、有责任感的男生组建家庭。",
  },
]);

// 根据筛选条件过滤用户
const filteredUsers = computed(() => {
  if (activeTab.value === "all") {
    return recommendUsers.value;
  } else if (activeTab.value === "nearby") {
    return recommendUsers.value.filter(
      (user) => parseFloat(user.location.split("·")[1].replace("km", "")) < 5
    );
  } else if (activeTab.value === "verified") {
    return recommendUsers.value.filter((user) => user.isVerified);
  } else if (activeTab.value === "new") {
    // 假设前两个是新用户
    return recommendUsers.value.slice(0, 2);
  }
  return recommendUsers.value;
});

// 缘分匹配用户数据
const matchedUsers = ref([
  {
    id: "4001",
    name: "小瑶",
    age: 28,
    avatar: "https://picsum.photos/300/400?random=20",
    distance: "1.5km",
    job: "幼师",
    education: "大专",
    isVerified: true,
    tags: ["温柔", "善解人意", "爱家"],
  },
  {
    id: "4002",
    name: "小洁",
    age: 26,
    avatar: "https://picsum.photos/300/400?random=21",
    distance: "2.3km",
    job: "护士",
    education: "本科",
    isVerified: true,
    tags: ["开朗", "会照顾人", "爱运动"],
  },
  {
    id: "4003",
    name: "小刚",
    age: 30,
    avatar: "https://picsum.photos/300/400?random=22",
    distance: "3.8km",
    job: "建筑工程师",
    education: "本科",
    isVerified: false,
    tags: ["踏实", "爱旅游", "懂生活"],
  },
  {
    id: "4004",
    name: "小妃",
    age: 25,
    avatar: "https://picsum.photos/300/400?random=23",
    distance: "5.2km",
    job: "销售",
    education: "大专",
    isVerified: true,
    tags: ["活泼", "爱读书", "会唱歌"],
  },
]);

// 本地相亲活动数据
const nearbyEvents = ref([
  {
    id: "5001",
    title: "周末游园对对碰相亲会",
    image: "https://picsum.photos/400/200?random=30",
    time: "2023-06-10 14:00-17:00",
    address: "星光公园中心广场",
    price: 68,
    isFull: false,
    maleCount: 12,
    femaleCount: 15,
    maleLimit: 20,
    femaleLimit: 20,
  },
  {
    id: "5002",
    title: "咖啡小聚相亲小组",
    image: "https://picsum.photos/400/200?random=31",
    time: "2023-06-17 19:00-21:00",
    address: "市中心COCO咖啡厅",
    price: 88,
    isFull: false,
    maleCount: 8,
    femaleCount: 6,
    maleLimit: 8,
    femaleLimit: 8,
  },
  {
    id: "5003",
    title: "掩面舞会相亲特别场",
    image: "https://picsum.photos/400/200?random=32",
    time: "2023-06-24 19:30-22:30",
    address: "晨曦酒店大安厅",
    price: 128,
    isFull: true,
    maleCount: 25,
    femaleCount: 25,
    maleLimit: 25,
    femaleLimit: 25,
  },
]);

// 本地红娘数据
const localMatchmakers = ref([
  {
    id: "6001",
    name: "王阳阳",
    avatar: "https://picsum.photos/300/300?random=40",
    rating: 4.9,
    years: 8,
    area: "市中心",
    successCount: 128,
    isOfficial: true,
    services: ["精准匹配", "家庭财务分析", "性格测试"],
  },
  {
    id: "6002",
    name: "张宝弘",
    avatar: "https://picsum.photos/300/300?random=41",
    rating: 4.7,
    years: 12,
    area: "西郊",
    successCount: 156,
    isOfficial: true,
    services: ["一对一约见", "结婚证书核实", "家庭调解"],
  },
  {
    id: "6003",
    name: "李春花",
    avatar: "https://picsum.photos/300/300?random=42",
    rating: 4.5,
    years: 5,
    area: "北区",
    successCount: 87,
    isOfficial: false,
    services: ["儿女相亲服务", "婚姻频道反馈", "恋爱心理诊断"],
  },
  {
    id: "6004",
    name: "陈志强",
    avatar: "https://picsum.photos/300/300?random=43",
    rating: 4.8,
    years: 10,
    area: "南区",
    successCount: 132,
    isOfficial: true,
    services: ["高端客户配对", "背景调查", "专属红娘"],
  },
]);

// 成功故事数据
const successStories = ref([
  {
    id: "7001",
    names: "小李和小瑶",
    maleAvatar: "https://picsum.photos/100/100?random=50",
    femaleAvatar: "https://picsum.photos/100/100?random=51",
    date: "2023年05月",
    location: "本地",
    description:
      "第一次相亲就一见钟情，三个月后我们决定在一起。感谢红娘王阳阳的精准匹配，让我们如此相配。",
    matchmakerName: "王阳阳",
    matchmakerAvatar: "https://picsum.photos/50/50?random=52",
    views: 2358,
  },
  {
    id: "7002",
    names: "小张和小林",
    maleAvatar: "https://picsum.photos/100/100?random=53",
    femaleAvatar: "https://picsum.photos/100/100?random=54",
    date: "2023年04月",
    location: "西郊",
    description:
      "参加相亲活动本来已经失去信心，没想到在咖啡厅小组第一次见面就懂得了彼此的心意。两个月就确定了关系，现在计划买房结婚。",
    matchmakerName: "张宝弘",
    matchmakerAvatar: "https://picsum.photos/50/50?random=55",
    views: 1876,
  },
  {
    id: "7003",
    names: "小陈和小雪",
    maleAvatar: "https://picsum.photos/100/100?random=56",
    femaleAvatar: "https://picsum.photos/100/100?random=57",
    date: "2023年03月",
    location: "北区",
    description:
      "当初父母介绍认识，我还有点抗拒。感谢红娘李阿姐的细心澄选，让我们能够平稳顺利地步入婚姻殿堂。",
    matchmakerName: "李春花",
    matchmakerAvatar: "https://picsum.photos/50/50?random=58",
    views: 2105,
  },
]);

// 用户操作
const likeUser = (id: string) => {
  const user = recommendUsers.value.find((u) => u.id === id);
  if (user) {
    user.isLiked = !user.isLiked;
    uni.showToast({
      title: user.isLiked ? "已喜欢" : "已取消喜欢",
      icon: "none",
    });
  }
};

const dislikeUser = (id: string) => {
  uni.showToast({
    title: "已跳过",
    icon: "none",
  });
  // 实际应用中可能需要将该用户从推荐列表中移除
};

const messageUser = (id: string) => {
  uni.navigateTo({
    url: `/pages/chat/conversation?userId=${id}`,
  });
};

// 约会攻略数据
const datingGuides = ref([
  {
    id: "3001",
    image: "https://picsum.photos/200/150?random=12",
    title: "第一次约会聊什么？教你避免尴尬冷场",
    description: "约会聊天话题技巧，让你轻松拉近两人距离，增进感情...",
    author: "情感顾问",
    authorAvatar: "https://picsum.photos/50/50?random=21",
    likeCount: "2.5k",
  },
  {
    id: "3002",
    image: "https://picsum.photos/200/150?random=13",
    title: "脱单秘籍：如何提升自己的魅力值",
    description:
      "从外表、谈吐到内在修养，全方位提升个人魅力，让你更容易获得异性青睐...",
    author: "形象顾问",
    authorAvatar: "https://picsum.photos/50/50?random=22",
    likeCount: "3.2k",
  },
  {
    id: "3003",
    image: "https://picsum.photos/200/150?random=14",
    title: "如何判断对方是否对你有好感？",
    description: "解读肢体语言和交流信号，让你准确把握对方的真实想法...",
    author: "心理专家",
    authorAvatar: "https://picsum.photos/50/50?random=23",
    likeCount: "1.8k",
  },
]);

// 底部导航配置
const currentTabIndex = ref(0);
const tabBarList = ref([
  {
    pagePath: "/pages/dating/match/index",
    text: "匹配",
    name: "favorite",
    customPrefix: "i-carbon"
  },
  {
    pagePath: "/pages/dating/offline-events/index", 
    text: "相亲会",
    name: "calendar",
    customPrefix: "i-carbon"
  },
  {
    pagePath: "/pages/dating/matchmaker/index",
    text: "红娘",
    name: "user-multiple",
    customPrefix: "i-carbon"
  },
  {
    pagePath: "/pages/dating/chat/index",
    text: "消息",
    name: "chat",
    customPrefix: "i-carbon",
    num: 3 // 未读消息数
  },
  {
    pagePath: "/pages/dating/profile/index",
    text: "我的",
    name: "user-profile",
    customPrefix: "i-carbon"
  }
]);

// 导航函数
const navigateToFeature = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};

const navigateTo = (url: string) => {
  uni.navigateTo({ url });
};

// 查看用户详情
const viewUserDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/user/detail?id=${id}`,
  });
};

// 查看相亲活动详情
const viewEventDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/offline-events/detail?id=${id}`,
  });
};

// 查看红娘详情
const viewMatchmakerDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/matchmakers/detail?id=${id}`,
  });
};

const viewActivityDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/activity/detail?id=${id}`,
  });
};

const viewGuideDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/guide/detail?id=${id}`,
  });
};

const viewSuccessStory = (id: string) => {
  uni.navigateTo({
    url: `/pages/dating/success-story/detail?id=${id}`,
  });
};

// 底部导航点击事件
const onTabBarClick = (e: any) => {
  const { index, pagePath } = e;
  currentTabIndex.value = index;
  
  if (pagePath) {
    uni.navigateTo({
      url: pagePath
    });
  }
};

// Banner点击
const onBannerClick = (item: any) => {
  uni.navigateTo({
    url: item.link,
  });
};
</script>

<style lang="scss" scoped>
// 页面容器
.dating-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
}

// 顶部导航栏
.navbar-container {
  .navbar {
    height: 90rpx;
    background: white;
  }

  .navbar-actions {
    .action-icon {
      position: relative;
    }

    .notification-badge {
      position: absolute;
      top: 0;
      right: 0;
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #ff5778;
    }
  }
}

// 轮播图
.banner-section {
  .banner-swiper {
    height: 300rpx;
    position: relative;
  }

  .banner-image {
    width: 100%;
    height: 300rpx;
  }

  .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 50%,
      rgba(0, 0, 0, 0.5) 100%
    );
  }

  .banner-content {
    position: absolute;
    bottom: 30rpx;
    left: 30rpx;
    color: white;
    z-index: 1;

    .banner-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 6rpx;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }

    .banner-subtitle {
      font-size: 24rpx;
      opacity: 0.9;
    }
  }
}

// 功能导航
.features-section {
  .feature-icon-bg {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .feature-name {
    margin-top: 10rpx;
  }
}

// 标题指示器
.title-indicator {
  width: 6rpx;
  height: 30rpx;
  background-color: #ff5778;
  margin-right: 15rpx;
  border-radius: 3rpx;
}

.color-primary {
  color: #ff5778;
}

// 隐藏滚动条
.hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
}

// 活动卡片
.activities-section {
  .activity-card {
    width: 300rpx;
    border-radius: 15rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
    background-color: white;

    .activity-image-container {
      position: relative;
    }

    .activity-image {
      width: 300rpx;
      height: 180rpx;
    }

    .activity-status {
      position: absolute;
      top: 15rpx;
      right: 15rpx;
      font-size: 20rpx;
      color: white;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      background-color: rgba(0, 0, 0, 0.5);

      &.status-hot {
        background-color: rgba(255, 87, 120, 0.8);
      }

      &.status-full {
        background-color: rgba(150, 150, 150, 0.8);
      }
    }

    .activity-type-tag {
      position: absolute;
      bottom: 15rpx;
      left: 15rpx;
      font-size: 20rpx;
      color: white;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
    }

    .activity-content {
      background-color: white;
    }

    .text-primary {
      color: #ff5778;
    }

    .people-count-tag {
      font-size: 20rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 6rpx 12rpx;
      border-radius: 4rpx;
    }
  }
}

// 用户推荐区域
.recommendation-section {
  .filter-tabs {
    .filter-tab {
      color: #666;
      font-size: 26rpx;
      transition: all 0.3s;
    }

    .active-tab {
      color: white;
      background-color: #ff5778;
    }
  }

  .user-cards-swiper {
    height: 800rpx;
    width: 100%;
  }

  .user-swiper-item {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
  }

  .user-card-big {
    width: 580rpx;
    border-radius: 20rpx;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
    transform: scale(0.9);
    transition: all 0.3s;

    &.card-active {
      transform: scale(1);
    }

    .user-card-image-container {
      position: relative;
      height: 500rpx;
    }

    .user-card-image {
      width: 100%;
      height: 100%;
    }

    .user-card-gradient {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 150rpx;
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(0, 0, 0, 0) 100%
      );
    }

    .vip-badge {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ffcc00;
    }

    .verified-badge {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      background-color: rgba(0, 0, 0, 0.3);
      color: white;
      font-size: 22rpx;
      padding: 6rpx 15rpx;
      border-radius: 25rpx;
      display: flex;
      align-items: center;
    }

    .user-card-actions {
      .action-btn {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 15rpx;
        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
        font-size: 40rpx;
      }

      .action-dislike {
        background-color: white;
        color: #ff5252;
      }

      .action-like {
        background-color: #ff5778;
        color: white;
      }

      .action-message {
        background-color: white;
        color: #4a90e2;
      }
    }

    .user-card-info {
      padding: 25rpx;
    }

    .gender-icon {
      width: 28rpx;
      height: 28rpx;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      color: #666;
    }

    .user-tag {
      font-size: 22rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 4rpx 15rpx;
      border-radius: 20rpx;
      margin-right: 15rpx;
      margin-bottom: 10rpx;
    }
  }

  .swiper-indicators {
    margin-top: 20rpx;

    .indicator-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #ddd;
      margin: 0 8rpx;
      transition: all 0.3s;

      &.active-dot {
        width: 24rpx;
        border-radius: 6rpx;
        background-color: #ff5778;
      }
    }
  }
}

// 约会攻略
.dating-guides-section {
  .guide-item {
    transition: all 0.3s;

    &:active {
      transform: scale(0.98);
    }
  }

  .guide-image {
    width: 200rpx;
    height: 150rpx;
    flex-shrink: 0;
  }

  .author-avatar {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
  }
}

// 成功案例
.success-stories-section {
  .success-story-item {
    transition: all 0.3s;

    &:active {
      transform: scale(0.98);
    }
  }

  .couple-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
  }
}



// 通用样式
.color-grey {
  color: #999;
}

.rounded-20rpx {
  border-radius: 20rpx;
}

.rounded-15rpx {
  border-radius: 15rpx;
}

.rounded-10rpx {
  border-radius: 10rpx;
}

.position-relative {
  position: relative;
}
</style>

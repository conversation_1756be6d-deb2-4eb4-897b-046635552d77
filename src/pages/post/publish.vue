<template>
  <view class="publish-container">
    <uni-nav-bar
      :fixed="true"
      :border="false"
      status-bar
      left-icon="back"
      title="发布动态"
      @clickLeft="goBack"
    >
      <template #right>
        <view class="publish-btn" @tap="submitPost">发布</view>
      </template>
    </uni-nav-bar>

    <view class="content-container">
      <!-- 输入区域 -->
      <view class="input-area">
        <textarea
          class="content-input"
          v-model="postContent"
          placeholder="分享你的最新动态..."
          maxlength="2000"
          show-confirm-bar="false"
        ></textarea>
        <view class="text-right text-24rpx color-grey mt-10rpx mr-20rpx">
          {{ postContent.length }}/2000
        </view>
      </view>

      <!-- 图片/视频展示区 -->
      <view
        class="media-container mx-20rpx"
        v-if="images.length > 0 || videoPath"
      >
        <!-- 图片展示 -->
        <view class="image-grid" v-if="images.length > 0 && !videoPath">
          <view
            class="image-item"
            v-for="(image, index) in images"
            :key="index"
          >
            <image :src="image" class="preview-image" mode="aspectFill"></image>
            <view class="delete-icon" @tap.stop="removeImage(index)">
              <text class="i-carbon-close-filled"></text>
            </view>
          </view>
          <view
            class="add-image-btn"
            v-if="images.length < 9"
            @tap="chooseImage"
          >
            <text class="i-carbon-add color-grey"></text>
          </view>
        </view>

        <!-- 视频展示 -->
        <view class="video-container" v-if="videoPath">
          <video
            :src="videoPath"
            class="preview-video"
            :poster="videoCover"
            object-fit="cover"
          ></video>
          <view class="video-delete-icon" @tap="removeVideo">
            <text class="i-carbon-close-filled"></text>
          </view>
        </view>
      </view>

      <!-- 位置信息 -->
      <view class="location-container mx-20rpx mb-20rpx" v-if="location">
        <view class="location-info flex items-center">
          <text class="i-carbon-location text-primary mr-10rpx"></text>
          <text class="text-26rpx">{{ location }}</text>
          <view class="location-delete ml-auto" @tap="removeLocation">
            <text class="i-carbon-close color-grey"></text>
          </view>
        </view>
      </view>

      <!-- 功能按钮 -->
      <view
        class="function-bar flex items-center mx-20rpx p-20rpx bg-white rounded-lg"
      >
        <view class="function-item" @tap="chooseImage">
          <text class="i-carbon-image color-info"></text>
          <text class="function-text">图片</text>
        </view>
        <view class="function-item" @tap="chooseVideo">
          <text class="i-carbon-video color-info"></text>
          <text class="function-text">视频</text>
        </view>
        <view class="function-item" @tap="chooseLocation">
          <text class="i-carbon-location color-info"></text>
          <text class="function-text">位置</text>
        </view>
        <view class="function-item" @tap="chooseTag">
          <text class="i-carbon-tag color-info"></text>
          <text class="function-text">话题</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 动态内容
const postContent = ref("");

// 图片列表
const images = ref<string[]>([]);

// 视频路径
const videoPath = ref("");

// 视频封面
const videoCover = ref("");

// 位置信息
const location = ref("");

// 选择图片
const chooseImage = () => {
  if (videoPath.value) {
    uni.showToast({
      title: "已添加视频，不能同时添加图片",
      icon: "none",
    });
    return;
  }

  uni.chooseImage({
    count: 9 - images.value.length,
    sizeType: ["compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      const selectedImages = res.tempFilePaths as string[];
      images.value = [...images.value, ...selectedImages];
    },
  });
};

// 移除图片
const removeImage = (index: number) => {
  images.value.splice(index, 1);
};

// 选择视频
const chooseVideo = () => {
  if (images.value.length > 0) {
    uni.showToast({
      title: "已添加图片，不能同时添加视频",
      icon: "none",
    });
    return;
  }

  uni.chooseVideo({
    sourceType: ["album", "camera"],
    maxDuration: 60,
    camera: "back",
    compressed: true,
    success: (res) => {
      videoPath.value = res.tempFilePath;
      // 生成视频封面
      videoCover.value = "";
      // 这里应该调用接口生成视频封面，但这里简化处理
      setTimeout(() => {
        videoCover.value = "https://picsum.photos/seed/video/400/300";
      }, 1000);
    },
  });
};

// 移除视频
const removeVideo = () => {
  videoPath.value = "";
  videoCover.value = "";
};

// 选择位置
const chooseLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      location.value = res.name || res.address;
    },
    fail: () => {
      uni.showToast({
        title: "位置获取失败",
        icon: "none",
      });
    },
  });
};

// 移除位置
const removeLocation = () => {
  location.value = "";
};

// 选择话题
const chooseTag = () => {
  uni.showToast({
    title: "话题功能开发中",
    icon: "none",
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 提交动态
const submitPost = () => {
  if (
    !postContent.value.trim() &&
    images.value.length === 0 &&
    !videoPath.value
  ) {
    uni.showToast({
      title: "请输入内容或添加图片/视频",
      icon: "none",
    });
    return;
  }

  // 提交前显示loading
  uni.showLoading({
    title: "发布中...",
  });

  // 模拟提交
  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: "发布成功",
      icon: "success",
      duration: 2000,
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      },
    });
  }, 2000);

  // 实际项目中应该上传图片和视频到服务器，然后提交动态内容
  // 这里省略上传逻辑
};
</script>

<style lang="scss" scoped>
.publish-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content-container {
  padding-top: 20rpx;
}

.publish-btn {
  font-size: 30rpx;
  padding: 0 20rpx;
  color: $primary;
  font-weight: bold;
}

.input-area {
  background-color: white;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.content-input {
  padding: 20rpx;
  min-height: 200rpx;
  width: 100%;
  font-size: 30rpx;
}

.media-container {
  background-color: white;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
}

.image-item,
.add-image-btn {
  width: calc(33.33% - 16rpx);
  height: 200rpx;
  margin: 8rpx;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-icon,
.video-delete-icon {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.add-image-btn {
  border: 1rpx dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50rpx;
  border-radius: 8rpx;
}

.video-container {
  width: 100%;
  height: 400rpx;
  position: relative;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: 100%;
}

.function-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.function-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 40rpx;
}

.function-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.location-container {
  background-color: white;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
}

.location-info {
  font-size: 28rpx;
}

.text-primary {
  color: $primary;
}
</style>

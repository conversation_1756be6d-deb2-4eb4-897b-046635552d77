<template>
  <view class="rent-list-page">
    <!-- 使用z-paging组件代替scroll-view -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-scrollbar="false"
      @scrolltolower="loadMore"
      :refresher-threshold="80"
      @refresherrefresh="onRefresh"
    >
      <!-- 顶部搜索栏 -->
      <view class="search-header">
        <view class="search-box" @tap="goToSearch">
          <text class="i-carbon-search text-28rpx color-grey mr-12rpx"></text>
          <text class="search-placeholder">搜索小区、地标、学校</text>
        </view>
      </view>
      <!-- 功能区导航栏 -->
      <scroll-view class="function-nav" scroll-x="true" show-scrollbar="false">
        <view class="nav-list">
          <view
            v-for="(item, index) in functionNavs"
            :key="index"
            class="nav-item"
            :class="{ active: activeFunctionIndex === index }"
            @tap="switchFunction(index)"
          >
            <view
              class="nav-icon-container"
              :class="{ active: activeFunctionIndex === index }"
              :style="{
                backgroundColor:
                  activeFunctionIndex === index
                    ? '#FF6D00'
                    : 'rgba(0,0,0,0.05)',
                borderColor:
                  activeFunctionIndex === index ? '#FF6D00' : item.color,
              }"
            >
              <text
                :class="[item.icon, 'nav-icon']"
                :style="{
                  color: activeFunctionIndex === index ? '#ffffff' : item.color,
                }"
              ></text>
            </view>
            <text
              class="nav-text"
              :style="{
                color: activeFunctionIndex === index ? '#FF6D00' : '#666',
              }"
              >{{ item.name }}</text
            >
            <view
              v-if="activeFunctionIndex === index"
              class="nav-indicator"
            ></view>
          </view>
        </view>
      </scroll-view>

      <!-- 筛选条件区域 - 添加sticky类 -->
      <view class="filter-section sticky-header">
        <!-- 第一行筛选条件 -->
        <view class="filter-row">
          <view
            v-for="(filter, index) in firstRowFilters"
            :key="index"
            class="filter-item"
            :class="{ active: filter.hasSelected }"
            @tap="showFilterPanel(filter.type)"
          >
            <text class="filter-text">{{
              filter.selectedText || filter.defaultText
            }}</text>
            <text
              class="i-carbon-chevron-down text-20rpx ml-8rpx transition-transform"
              :class="{
                'rotate-180': currentFilterType === filter.type && showFilter,
              }"
            ></text>
          </view>
        </view>

        <!-- 第二行排序选项 -->
        <view class="sort-row">
          <view
            v-for="(sort, index) in sortOptions"
            :key="index"
            class="sort-item"
            :class="{ active: activeSortIndex === index }"
            @tap="switchSort(index)"
          >
            <text class="sort-text">{{ sort.name }}</text>
            <text
              v-if="sort.hasDirection && activeSortIndex === index"
              :class="[
                'i-carbon-chevron-up',
                'sort-arrow',
                { 'rotate-180': sort.direction === 'desc' },
              ]"
            ></text>
          </view>
        </view>
      </view>

      <!-- 房源列表 -->
      <view class="house-list">
        <view
          v-for="(house, index) in houseList"
          :key="house.id"
          class="house-card"
          @tap="goToDetail(house.id)"
        >
          <!-- 房源图片 -->
          <view class="image-container">
            <image
              :src="house.image"
              mode="aspectFill"
              class="house-image"
              :lazy-load="true"
            />
            <!-- VR看房标签 -->
            <view v-if="house.hasVR" class="vr-tag">
              <text class="i-carbon-view text-20rpx mr-6rpx"></text>
              <text class="vr-text">VR看房</text>
            </view>
            <!-- 新上标签 -->
            <view v-if="house.isNew" class="new-tag">新上</view>
          </view>

          <!-- 房源信息 -->
          <view class="house-info">
            <!-- 上半部分：标题和基本信息 -->
            <view class="info-top">
              <!-- 标题行 -->
              <text class="house-title">{{ formatTitle(house) }}</text>

              <!-- 房源详情 -->
              <text class="details">{{ formatDetails(house) }}</text>

              <!-- 标签区域 -->
              <view class="tags-container">
                <text
                  v-for="(tag, tagIndex) in house.tags.slice(0, 4)"
                  :key="tagIndex"
                  class="tag"
                  >{{ tag }}</text
                >
              </view>
            </view>

            <!-- 下半部分：价格信息 -->
            <view class="info-bottom">
              <view class="price-row">
                <text class="price">{{ house.price }}</text>
                <text class="price-unit">元/月</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <!-- 筛选面板遮罩 -->
    <view v-if="showFilter" class="filter-mask" @tap="hideFilterPanel"></view>

    <!-- 筛选面板 -->
    <view v-if="showFilter" class="filter-panel" :class="{ show: showFilter }">
      <view class="panel-header">
        <text class="panel-title">{{ currentFilterTitle }}</text>
        <view class="panel-actions">
          <text class="reset-btn" @tap="resetCurrentFilter">重置</text>
          <text class="confirm-btn" @tap="confirmFilter">确定</text>
        </view>
      </view>
      <view class="panel-content">
        <view class="options-grid">
          <view
            v-for="(option, index) in currentFilterOptions"
            :key="index"
            class="option-item"
            :class="{ selected: option.selected }"
            @tap="selectOption(option)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";

// 房源数据接口定义
interface RentHouse {
  id: string;
  title: string;
  community: string;
  layout: string; // 如"2室1厅1卫"
  area: string;
  floor: string;
  direction: string;
  price: string;
  location: string;
  tags: string[];
  image: string;
  hasVR?: boolean;
  isNew?: boolean;
  paymentMethod?: string; // 如"押一付一"
  utilities?: string; // 如"民水民电"
  rentType?: string; // 整租/合租
}

// 筛选选项接口
interface FilterOption {
  label: string;
  value: string;
  selected: boolean;
}

// 页面状态
const loading = ref(false);
const isRefreshing = ref(false);
const networkError = ref(false);
const showFilter = ref(false);
const currentFilterType = ref("");
const currentFilterTitle = ref("");
const activeFunctionIndex = ref(0);
const activeSortIndex = ref(0);

// z-paging实例引用
const paging = ref(null);

// 功能导航数据 - 使用更美观的图标
const functionNavs = reactive([
  {
    name: "公寓",
    icon: "i-carbon-building-insights-3",
    type: "apartment",
    color: "#FF6B35",
  },
  {
    name: "合租",
    icon: "i-carbon-user-multiple",
    type: "shared",
    color: "#4ECDC4",
  },
  { name: "整租", icon: "i-carbon-home", type: "whole", color: "#45B7D1" },
  {
    name: "品牌公寓",
    icon: "i-carbon-star-filled",
    type: "brand",
    color: "#F7DC6F",
  },
  {
    name: "长租公寓",
    icon: "i-carbon-time-filled",
    type: "longterm",
    color: "#BB8FCE",
  },
  {
    name: "民宿",
    icon: "i-carbon-location-heart",
    type: "bnb",
    color: "#F1948A",
  },
]);

// 第一行筛选条件
const firstRowFilters = reactive([
  {
    type: "price",
    defaultText: "价格",
    selectedText: "",
    hasSelected: false,
  },
  {
    type: "layout",
    defaultText: "户型",
    selectedText: "",
    hasSelected: false,
  },
  {
    type: "decoration",
    defaultText: "装修",
    selectedText: "",
    hasSelected: false,
  },
  {
    type: "direction",
    defaultText: "朝向",
    selectedText: "",
    hasSelected: false,
  },
]);

// 排序选项
const sortOptions = reactive([
  { name: "默认排序", type: "default", hasDirection: false },
  { name: "价格", type: "price", hasDirection: true, direction: "asc" },
  { name: "最新发布", type: "time", hasDirection: false },
]);

// 房源列表数据
const houseList = ref<RentHouse[]>([]);

// 当前筛选选项
const currentFilterOptions = ref<FilterOption[]>([]);

// 筛选选项数据
const filterOptionsData = {
  price: [
    { label: "不限", value: "", selected: true },
    { label: "1000以下", value: "0-1000", selected: false },
    { label: "1000-2000", value: "1000-2000", selected: false },
    { label: "2000-3000", value: "2000-3000", selected: false },
    { label: "3000-5000", value: "3000-5000", selected: false },
    { label: "5000-8000", value: "5000-8000", selected: false },
    { label: "8000以上", value: "8000-", selected: false },
  ],
  layout: [
    { label: "不限", value: "", selected: true },
    { label: "1室", value: "1", selected: false },
    { label: "2室", value: "2", selected: false },
    { label: "3室", value: "3", selected: false },
    { label: "4室", value: "4", selected: false },
    { label: "5室及以上", value: "5+", selected: false },
  ],
  decoration: [
    { label: "不限", value: "", selected: true },
    { label: "精装", value: "fine", selected: false },
    { label: "简装", value: "simple", selected: false },
    { label: "毛坯", value: "rough", selected: false },
  ],
  direction: [
    { label: "不限", value: "", selected: true },
    { label: "南", value: "south", selected: false },
    { label: "北", value: "north", selected: false },
    { label: "东", value: "east", selected: false },
    { label: "西", value: "west", selected: false },
    { label: "南北", value: "south-north", selected: false },
    { label: "东西", value: "east-west", selected: false },
  ],
};

// 页面方法
const goBack = () => {
  uni.navigateBack();
};

const goToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

const goToMap = () => {
  uni.navigateTo({
    url: "/pages/house/rent/map",
  });
};

const goToDetail = (houseId: string) => {
  uni.navigateTo({
    url: `/pages/house/detail/index?id=${houseId}`,
  });
};

// 功能导航切换
const switchFunction = (index: number) => {
  activeFunctionIndex.value = index;
  // 使用z-paging的reload方法重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 排序切换
const switchSort = (index: number) => {
  if (activeSortIndex.value === index && sortOptions[index].hasDirection) {
    // 切换排序方向
    sortOptions[index].direction =
      sortOptions[index].direction === "asc" ? "desc" : "asc";
  } else {
    activeSortIndex.value = index;
  }
  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 显示筛选面板
const showFilterPanel = (type: string) => {
  currentFilterType.value = type;
  currentFilterTitle.value = getFilterTitle(type);
  currentFilterOptions.value = JSON.parse(
    JSON.stringify(filterOptionsData[type as keyof typeof filterOptionsData])
  );
  showFilter.value = true;
};

// 隐藏筛选面板
const hideFilterPanel = () => {
  showFilter.value = false;
  currentFilterType.value = "";
};

// 获取筛选标题
const getFilterTitle = (type: string) => {
  const titles: Record<string, string> = {
    price: "价格区间",
    layout: "户型选择",
    decoration: "装修情况",
    direction: "朝向筛选",
  };
  return titles[type] || "";
};

// 选择筛选选项
const selectOption = (option: FilterOption) => {
  // 单选逻辑
  currentFilterOptions.value.forEach((opt) => (opt.selected = false));
  option.selected = true;
};

// 重置当前筛选
const resetCurrentFilter = () => {
  currentFilterOptions.value.forEach((opt, index) => {
    opt.selected = index === 0; // 第一个选项（不限）为选中状态
  });
};

// 确认筛选
const confirmFilter = () => {
  const selectedOption = currentFilterOptions.value.find((opt) => opt.selected);
  const filterIndex = firstRowFilters.findIndex(
    (f) => f.type === currentFilterType.value
  );

  if (filterIndex !== -1 && selectedOption) {
    firstRowFilters[filterIndex].selectedText =
      selectedOption.label === "不限" ? "" : selectedOption.label;
    firstRowFilters[filterIndex].hasSelected = selectedOption.label !== "不限";
  }

  // 更新原始数据
  const filterKey = currentFilterType.value as keyof typeof filterOptionsData;
  filterOptionsData[filterKey] = JSON.parse(
    JSON.stringify(currentFilterOptions.value)
  );

  hideFilterPanel();
  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 格式化标题
const formatTitle = (house: RentHouse) => {
  const rentType = house.rentType || "整租";
  const rooms = house.layout.match(/(\d+)室/)
    ? house.layout.match(/(\d+)室/)![1]
    : "";
  return `${rentType}${rooms}居 · ${house.community}`;
};

// 格式化详情
const formatDetails = (house: RentHouse) => {
  const parts = [];
  if (house.area) parts.push(`${house.area}㎡`);
  if (house.layout) parts.push(house.layout);
  if (house.floor) parts.push(house.floor);
  if (house.direction) parts.push(house.direction);
  return parts.join(" · ");
};

// z-paging查询房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 模拟网络请求延迟
  setTimeout(() => {
    // 模拟数据
    const mockData: RentHouse[] = [
      {
        id: "1",
        title: "整租2居 · 香山南营66号",
        community: "香山南营66号",
        layout: "2室1厅1卫",
        area: "70",
        floor: "高楼层",
        direction: "南北",
        price: "4600",
        location: "西山",
        tags: ["南北通透", "高楼层", "步梯"],
        image: "https://picsum.photos/seed/rent1/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "2",
        title: "合租3居 · 朝阳嘉园",
        community: "朝阳嘉园",
        layout: "3室2厅2卫",
        area: "120",
        floor: "中楼层",
        direction: "东南",
        price: "2800",
        location: "朝阳区",
        tags: ["精装修", "近地铁", "拎包入住"],
        image: "https://picsum.photos/seed/rent2/240/200",
        hasVR: false,
        isNew: true,
        paymentMethod: "押一付三",
        utilities: "商水商电",
        rentType: "合租",
      },
      {
        id: "3",
        title: "整租1居 · 金茂府",
        community: "金茂府",
        layout: "1室1厅1卫",
        area: "45",
        floor: "低楼层",
        direction: "南",
        price: "3200",
        location: "朝阳区",
        tags: ["精装修", "家电齐全", "近商圈"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "4",
        title: "合租4居 · 万科如园",
        community: "万科如园",
        layout: "4室2厅2卫",
        area: "150",
        floor: "高楼层",
        direction: "南北",
        price: "3500",
        location: "海淀区",
        tags: ["南北通透", "采光好", "交通便利"],
        image: "https://picsum.photos/seed/rent4/240/200",
        hasVR: false,
        isNew: true,
        paymentMethod: "押一付二",
        utilities: "民水民电",
        rentType: "合租",
      },
      {
        id: "5",
        title: "整租1居 · 金茂府",
        community: "金茂府",
        layout: "1室1厅1卫",
        area: "45",
        floor: "低楼层",
        direction: "南",
        price: "3200",
        location: "朝阳区",
        tags: ["精装修", "家电齐全", "近商圈"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "6",
        title: "整租1居 · 金茂府",
        community: "金茂府",
        layout: "1室1厅1卫",
        area: "45",
        floor: "低楼层",
        direction: "南",
        price: "3200",
        location: "朝阳区",
        tags: ["精装修", "家电齐全", "近商圈"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
      {
        id: "7",
        title: "整租1居 · 金茂府",
        community: "金茂府",
        layout: "1室1厅1卫",
        area: "45",
        floor: "低楼层",
        direction: "南",
        price: "3200",
        location: "朝阳区",
        tags: ["精装修", "家电齐全", "近商圈"],
        image: "https://picsum.photos/seed/rent3/240/200",
        hasVR: true,
        isNew: false,
        paymentMethod: "押一付一",
        utilities: "民水民电",
        rentType: "整租",
      },
    ];

    // 告知z-paging加载完成
    if (paging.value) {
      if (pageNo >= 3) {
        // 模拟没有更多数据
        paging.value.complete([]);
      } else {
        paging.value.complete(mockData);
      }
    }
  }, 1000);
};

// 下拉刷新
const onRefresh = () => {
  if (paging.value) {
    paging.value.reload();
  }
};

// 加载更多 - 这个方法在z-paging中已自动处理
const loadMore = () => {
  // z-paging会自动处理加载更多逻辑
  console.log("加载更多");
};

// 页面加载时初始化数据
onMounted(() => {
  // z-paging会自动在挂载后调用queryHouseList
});
</script>

<style lang="scss" scoped>
.rent-list-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部搜索栏 */
.search-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  height: 64rpx;
  background-color: #f8f9fa;
  border-radius: 32rpx;
  padding: 0 24rpx;
  margin: 0 16rpx;
  transition: all 0.3s ease;

  &:active {
    background-color: #e9ecef;
  }
}

.search-placeholder {
  font-size: 28rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 功能导航栏 */
.function-nav {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
}

.nav-list {
  display: flex;
  padding: 0 24rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 32rpx;
  position: relative;
  transition: all 0.3s ease;

  &.active {
    .nav-text {
      font-weight: 600;
    }
  }
}

.nav-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &.active {
    transform: scale(1.1);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
  }
}

.nav-icon {
  font-size: 40rpx;
  transition: all 0.3s ease;
}

.nav-text {
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff6d00;
  border-radius: 2rpx;
}

/* 筛选条件区域 */
.filter-section {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 添加吸顶样式 */
.sticky-header {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 99;
}

.filter-row,
.sort-row {
  display: flex;
  padding: 0 24rpx;
}

.filter-item,
.sort-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  position: relative;
  transition: all 0.3s ease;

  &.active {
    .filter-text,
    .sort-text {
      color: #ff6d00;
      font-weight: 600;
    }
  }
}

.filter-text,
.sort-text {
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

.sort-arrow {
  font-size: 20rpx;
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* 房源列表 */
.house-list {
  padding: 24rpx;
}

/* 房源卡片 */
.house-card {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-container {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  flex-shrink: 0;
}

.house-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-top-left-radius: 16rpx;
  border-bottom-left-radius: 16rpx;
}

.vr-tag {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
}

.vr-text {
  font-size: 22rpx;
}

.new-tag {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #ff5a5f;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 16rpx;
  border-top-left-radius: 12rpx;
  border-bottom-right-radius: 8rpx;
}

/* 房源信息 */
.house-info {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.info-top {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-bottom {
  margin-top: 8rpx;
}

.house-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 0;
}

.price-row {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6d00;
}

.price-unit {
  font-size: 26rpx;
  color: #ff6d00;
  margin-left: 6rpx;
}

.details {
  font-size: 24rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  padding: 6rpx 12rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 22rpx;
  border-radius: 6rpx;
  font-weight: 500;
}

/* 筛选面板 */
.filter-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 200;
}

.filter-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  z-index: 201;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;

  &.show {
    transform: translateY(0);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.panel-actions {
  display: flex;
  gap: 32rpx;
}

.reset-btn,
.confirm-btn {
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.reset-btn {
  color: #666;
  background-color: #f8f9fa;

  &:active {
    background-color: #e9ecef;
  }
}

.confirm-btn {
  color: white;
  background-color: #ff6d00;

  &:active {
    background-color: #e55a00;
  }
}

.panel-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.option-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  transition: all 0.3s ease;

  &.selected {
    background-color: rgba(255, 109, 0, 0.1);
    border-color: #ff6d00;

    .option-text {
      color: #ff6d00;
      font-weight: 600;
    }
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-text {
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

/* 通用样式类 */
.color-main {
  color: #333;
}

.color-grey {
  color: #999;
}

.color-grey-light {
  color: #ccc;
}

.color-primary {
  color: #ff6d00;
}

.text-20rpx {
  font-size: 20rpx;
}

.text-28rpx {
  font-size: 28rpx;
}

.text-32rpx {
  font-size: 32rpx;
}

.text-36rpx {
  font-size: 36rpx;
}

.text-120rpx {
  font-size: 120rpx;
}

.mr-6rpx {
  margin-right: 6rpx;
}

.mr-8rpx {
  margin-right: 8rpx;
}

.mr-12rpx {
  margin-right: 12rpx;
}

.mr-16rpx {
  margin-right: 16rpx;
}

.ml-8rpx {
  margin-left: 8rpx;
}

.mb-24rpx {
  margin-bottom: 24rpx;
}
</style>

/**
 * 房源模拟数据
 */
export const houseListData = [
    {
        id: 'h001',
        title: '阳光花园 精装修两居室 南北通透 拎包入住',
        images: [
            'https://picsum.photos/seed/house101/800/600',
            'https://picsum.photos/seed/house102/800/600',
            'https://picsum.photos/seed/house103/800/600',
            'https://picsum.photos/seed/house104/800/600',
        ],
        tags: ['整租', '精装', '押一付一'],
        location: '海淀区 - 五道口',
        distance: '距地铁13号线五道口站500米',
        price: 5800,
        priceUnit: '元/月',
        houseType: '2室1厅',
        area: 85,
        orientation: '南北',
        floor: '8/18层',
        decoration: '精装修',
        facilities: [1, 2, 3, 4, 5, 6, 7],
        releaseTime: '2023-10-25',
        viewCount: 156,
        isVip: true,
        isNew: true,
        description: '此房坐落于阳光花园小区，紧邻五道口地铁站，交通便利。小区环境优美，物业管理完善，安全性高。房屋精装修，家具家电齐全，拎包入住。采光充足，通风良好，是理想的居住选择。',
        rentType: '整租',
        paymentMethod: '押一付一',
        contactPerson: '王先生',
        contactPhone: '138****5678',
        viewingTime: '随时可看',
        checkInTime: '随时入住',
        addressDetail: '海淀区成府路28号阳光花园小区8号楼8层802室',
        estateInfo: {
            name: '阳光花园',
            buildYear: 2012,
            totalBuildings: 12,
            propertyFee: 2.5,
            propertyCompany: '海淀物业管理有限公司',
            developers: '北京建设开发公司',
        },
        surroundings: [
            { type: '交通', name: '五道口地铁站', distance: '500米' },
            { type: '教育', name: '清华大学', distance: '1.2公里' },
            { type: '教育', name: '五道口小学', distance: '800米' },
            { type: '购物', name: '五道口购物中心', distance: '600米' },
            { type: '餐饮', name: '海底捞火锅', distance: '700米' },
            { type: '医疗', name: '海淀社区医院', distance: '1公里' },
        ],
        location_coordinates: {
            latitude: 39.992736,
            longitude: 116.338639,
        }
    },
    {
        id: 'h002',
        title: '望京SOHO附近 一居室 近地铁 随时看房',
        images: [
            'https://picsum.photos/seed/house201/800/600',
            'https://picsum.photos/seed/house202/800/600',
            'https://picsum.photos/seed/house203/800/600',
        ],
        tags: ['合租', '押一付三'],
        location: '朝阳区 - 望京',
        distance: '距地铁15号线望京站800米',
        price: 3200,
        priceUnit: '元/月',
        houseType: '1室1厅',
        area: 45,
        orientation: '南',
        floor: '10/22层',
        decoration: '简装',
        facilities: [1, 3, 4, 5, 6],
        releaseTime: '2023-10-20',
        viewCount: 89,
        isVip: false,
        isNew: false,
        description: '房源位于望京SOHO附近，交通便利，生活设施完善。房间朝南，采光好。配有基础家具，可拎包入住。',
        rentType: '整租',
        paymentMethod: '押一付三',
        contactPerson: '李女士',
        contactPhone: '139****4321',
        viewingTime: '随时可看',
        checkInTime: '11月1日起',
        addressDetail: '朝阳区望京街道望京SOHO T3座1008室',
        estateInfo: {
            name: '望京SOHO',
            buildYear: 2014,
            totalBuildings: 3,
            propertyFee: 4.5,
            propertyCompany: '朝阳物业管理有限公司',
            developers: '北京SOHO中国',
        },
        surroundings: [
            { type: '交通', name: '望京地铁站', distance: '800米' },
            { type: '购物', name: '望京购物中心', distance: '500米' },
            { type: '餐饮', name: '星巴克咖啡', distance: '100米' },
            { type: '医疗', name: '望京医院', distance: '1.5公里' },
        ],
        location_coordinates: {
            latitude: 39.993537,
            longitude: 116.467377,
        }
    },
    {
        id: 'h003',
        title: '自如友家 · 天通苑北 三家合住 主卧朝南',
        images: [
            'https://picsum.photos/seed/house301/800/600',
            'https://picsum.photos/seed/house302/800/600',
            'https://picsum.photos/seed/house303/800/600',
        ],
        tags: ['合租', '自如管理', '独立卫浴'],
        location: '昌平区 - 天通苑',
        distance: '距地铁5号线天通苑站300米',
        price: 2600,
        priceUnit: '元/月',
        houseType: '3室1厅主卧',
        area: 20,
        orientation: '南',
        floor: '6/12层',
        decoration: '精装修',
        facilities: [1, 2, 3, 4, 5, 6, 7, 11, 14],
        releaseTime: '2023-10-24',
        viewCount: 112,
        isVip: true,
        isNew: true,
        description: '此房为自如友家，三居室中的主卧室，朝南采光好。室内配有独立卫生间，家具家电齐全，拎包入住。公共区域有客厅和厨房，合住氛围良好。',
        rentType: '合租',
        paymentMethod: '押一付三',
        contactPerson: '自如管家',
        contactPhone: '************',
        viewingTime: '预约看房',
        checkInTime: '随时入住',
        addressDetail: '昌平区天通苑北一区10号楼6层602室',
        estateInfo: {
            name: '天通苑北一区',
            buildYear: 2008,
            totalBuildings: 24,
            propertyFee: 1.8,
            propertyCompany: '天通苑物业',
            developers: '北京建工集团',
        },
        surroundings: [
            { type: '交通', name: '天通苑地铁站', distance: '300米' },
            { type: '购物', name: '华联商场', distance: '500米' },
            { type: '餐饮', name: '肯德基', distance: '400米' },
            { type: '教育', name: '天通苑小学', distance: '600米' },
        ],
        location_coordinates: {
            latitude: 40.063564,
            longitude: 116.419381,
        }
    }
];

/**
 * 房源模拟数据生成工具
 */
import type { BaseHouseItem, SecondHandHouseItem, NewHouseItem, RentHouseItem, CommercialHouseItem } from '@/types/house';

/**
 * 生成指定数量的随机房源数据
 * @param count 需要生成的数量
 * @param type 房源类型: 'second'(二手房), 'new'(新房), 'rent'(租房), 'commercial'(商铺办公)
 * @returns 房源数据数组
 */
export function generateMoreHouses(count: number, type: string = 'second'): any[] {
    const result = [];

    for (let i = 0; i < count; i++) {
        let house;

        switch (type) {
            case 'second':
                house = generateSecondHouse(i);
                break;
            case 'new':
                house = generateNewHouse(i);
                break;
            case 'rent':
                house = generateRentHouse(i);
                break;
            case 'commercial':
                house = generateCommercialHouse(i);
                break;
            default:
                house = generateSecondHouse(i);
        }

        result.push(house);
    }

    return result;
}

/**
 * 生成二手房数据
 */
function generateSecondHouse(index: number): SecondHandHouseItem {
    const id = `second${index}`;
    const layout = `${Math.floor(Math.random() * 3) + 1}室${Math.floor(Math.random() * 2) + 1}厅`;
    const area = Math.floor(Math.random() * 100) + 50;
    const price = Math.floor(Math.random() * 500) + 300;

    return {
        id,
        title: `精装${layout} 南北通透 采光好`,
        image: `https://picsum.photos/seed/second${index}/300/200`,
        layout,
        area: area.toString(),
        direction: ["南北通透", "东西向", "朝南", "朝东"][Math.floor(Math.random() * 4)],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${Math.floor(Math.random() * 30) + 20}层`,
        price,
        unitPrice: `${Math.floor(Math.random() * 20000) + 30000}元/㎡`,
        tags: ["满五年", "近地铁", "学区房", "南北通透", "精装修"].slice(0, Math.floor(Math.random() * 3) + 1),
        community: ["阳光小区", "丽都花园", "蓝天家园", "翠湖园"][Math.floor(Math.random() * 4)],
        rooms: parseInt(layout.split('室')[0]),
        halls: parseInt(layout.split('室')[1].split('厅')[0]),
    };
}

/**
 * 生成新房数据
 */
function generateNewHouse(index: number): NewHouseItem {
    const id = `new${index}`;
    const area = Math.floor(Math.random() * 50) + 80;

    return {
        id,
        name: `碧桂园·翡翠天境${Math.floor(Math.random() * 10) + 1}期`,
        image: `https://picsum.photos/seed/new${index}/300/200`,
        area: `${area}-${area + 50}`,
        location: ["朝阳区·CBD", "海淀区·中关村", "丰台区·科技园", "昌平区·回龙观"][Math.floor(Math.random() * 4)],
        price: Math.floor(Math.random() * 30000) + 20000,
        priceRange: `${Math.floor(Math.random() * 30000) + 20000}-${Math.floor(Math.random() * 20000) + 40000}元/㎡`,
        tags: ["近地铁", "学区房", "公园旁", "精装修", "品牌开发商"].slice(0, Math.floor(Math.random() * 3) + 1),
        status: ["在售", "即将开盘", "售罄"][Math.floor(Math.random() * 3)],
        buildTypes: ["高层", "洋房", "别墅"].slice(0, Math.floor(Math.random() * 2) + 1),
        openTime: `${Math.floor(Math.random() * 12) + 1}月${Math.floor(Math.random() * 28) + 1}日`,
        developer: ["碧桂园", "万科", "保利", "华润"][Math.floor(Math.random() * 4)],
        extraInfo: `关注${Math.floor(Math.random() * 1000) + 100}人`,
    };
}

/**
 * 生成租房数据
 */
function generateRentHouse(index: number): RentHouseItem {
    const id = `rent${index}`;
    const layout = `${Math.floor(Math.random() * 3) + 1}室${Math.floor(Math.random() * 2) + 1}厅`;
    const area = Math.floor(Math.random() * 50) + 30;
    const rentType = ["整租", "合租", "公寓"][Math.floor(Math.random() * 3)];

    return {
        id,
        title: `${rentType}·${["阳光嘉园", "丽都花园", "蓝天家园"][Math.floor(Math.random() * 3)]} ${layout}`,
        image: `https://picsum.photos/seed/rent${index}/300/200`,
        layout,
        area: area.toString(),
        direction: ["南北通透", "东西向", "朝南", "朝东"][Math.floor(Math.random() * 4)],
        floor: `${Math.floor(Math.random() * 20) + 1}层/${Math.floor(Math.random() * 30) + 20}层`,
        price: Math.floor(Math.random() * 3000) + 1000,
        rentType,
        paymentMethod: ["押一付一", "押一付三", "押二付三"][Math.floor(Math.random() * 3)],
        tags: ["拎包入住", "近地铁", "精装修", "家电齐全", "随时看房"].slice(0, Math.floor(Math.random() * 3) + 1),
        rooms: parseInt(layout.split('室')[0]),
        halls: parseInt(layout.split('室')[1].split('厅')[0]),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
    };
}

/**
 * 生成商业地产数据
 */
function generateCommercialHouse(index: number): CommercialHouseItem {
    const id = `comm${index}`;
    const area = Math.floor(Math.random() * 500) + 50;
    const type = ["商铺", "写字楼", "厂房", "仓库"][Math.floor(Math.random() * 4)];
    const priceType = Math.random() > 0.5 ? 'rent' : 'sale';

    // 根据类型设置不同的标题模板
    let title;
    switch (type) {
        case "商铺":
            title = ["临街旺铺", "购物中心商铺", "社区底商", "商业街店面"][Math.floor(Math.random() * 4)];
            break;
        case "写字楼":
            title = ["甲级写字楼", "商务中心", "创意园区", "独栋办公"][Math.floor(Math.random() * 4)];
            break;
        case "厂房":
            title = ["标准厂房", "钢构厂房", "生产车间", "工业园区"][Math.floor(Math.random() * 4)];
            break;
        case "仓库":
            title = ["标准仓库", "物流中心", "冷库", "保税仓"][Math.floor(Math.random() * 4)];
            break;
        default:
            title = "商业地产";
    }

    return {
        id,
        title: `${["朝阳区", "海淀区", "丰台区", "昌平区"][Math.floor(Math.random() * 4)]} ${title}`,
        image: `https://picsum.photos/seed/comm${index}/300/200`,
        type,
        area: area.toString(),
        location: `${["商圈", "开发区", "产业园", "CBD"][Math.floor(Math.random() * 4)]}附近`,
        price: priceType === 'sale' ? (Math.floor(Math.random() * 500) + 100) : (Math.floor(Math.random() * 20000) + 3000),
        priceType,
        unitPrice: priceType === 'sale' ? `${Math.floor(Math.random() * 20000) + 10000}元/㎡` : null,
        tags: ["临街", "独立产权", "精装修", "近地铁", "高层", "配套齐全"].slice(0, Math.floor(Math.random() * 3) + 1),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
    };
}

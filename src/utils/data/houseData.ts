/**
 * 租房找房模块 - 通用数据
 */

// 城市数据
export const cityData = [
    { id: 1, name: '杭州市', hot: true },
    { id: 2, name: '温州市', hot: true },
    { id: 3, name: '宁波市', hot: true },
    { id: 4, name: '金华市', hot: false },
    { id: 5, name: '嘉兴市', hot: false },
    { id: 6, name: '台州市', hot: false },
    { id: 7, name: '绍兴市', hot: false },
    { id: 8, name: '湖州市', hot: false },
    { id: 9, name: '丽水市', hot: false },
    { id: 10, name: '衢州市', hot: false },
    { id: 11, name: '舟山市', hot: false },
];

// 区域数据（以杭州为例）
export const areaData = [
    {
        id: 1,
        name: '全城',
        subAreas: []
    },
    {
        id: 2,
        name: '热门商圈',
        subAreas: [
            { id: 21, name: '城西银泰', parentId: 2 },
            { id: 22, name: '西湖文化广场', parentId: 2 },
            { id: 23, name: '武林广场', parentId: 2 },
            { id: 24, name: '钱江新城', parentId: 2 },
            { id: 25, name: '滨江区政府', parentId: 2 },
        ]
    },
    {
        id: 3,
        name: '上城区',
        subAreas: [
            { id: 31, name: '清波街道', parentId: 3 },
            { id: 32, name: '湖滨街道', parentId: 3 },
            { id: 33, name: '小营街道', parentId: 3 },
        ]
    },
    {
        id: 4,
        name: '下城区',
        subAreas: [
            { id: 41, name: '武林街道', parentId: 4 },
            { id: 42, name: '天水街道', parentId: 4 },
            { id: 43, name: '长庆街道', parentId: 4 },
        ]
    },
    {
        id: 5,
        name: '西湖区',
        subAreas: [
            { id: 51, name: '灵隐街道', parentId: 5 },
            { id: 52, name: '西溪街道', parentId: 5 },
            { id: 53, name: '文新街道', parentId: 5 },
        ]
    },
    {
        id: 6,
        name: '拱墅区',
        subAreas: [
            { id: 61, name: '大关街道', parentId: 6 },
            { id: 62, name: '拱宸桥街道', parentId: 6 },
            { id: 63, name: '祥符街道', parentId: 6 },
        ]
    },
    {
        id: 7,
        name: '江干区',
        subAreas: [
            { id: 71, name: '笕桥街道', parentId: 7 },
            { id: 72, name: '凯旋街道', parentId: 7 },
            { id: 73, name: '钱江新城', parentId: 7 },
        ]
    },
    {
        id: 8,
        name: '滨江区',
        subAreas: [
            { id: 81, name: '西兴街道', parentId: 8 },
            { id: 82, name: '长河街道', parentId: 8 },
            { id: 83, name: '浦沿街道', parentId: 8 },
        ]
    },
    {
        id: 9,
        name: '余杭区',
        subAreas: [
            { id: 91, name: '临平街道', parentId: 9 },
            { id: 92, name: '南苑街道', parentId: 9 },
            { id: 93, name: '星桥街道', parentId: 9 },
        ]
    },
    {
        id: 10,
        name: '萧山区',
        subAreas: [
            { id: 101, name: '北干街道', parentId: 10 },
            { id: 102, name: '蜀山街道', parentId: 10 },
            { id: 103, name: '城厢街道', parentId: 10 },
        ]
    },
    {
        id: 11,
        name: '富阳区',
        subAreas: [
            { id: 111, name: '富春街道', parentId: 11 },
            { id: 112, name: '春江街道', parentId: 11 },
            { id: 113, name: '东洲街道', parentId: 11 },
        ]
    },
    {
        id: 12,
        name: '临安区',
        subAreas: [
            { id: 121, name: '锦城街道', parentId: 12 },
            { id: 122, name: '玲珑街道', parentId: 12 },
            { id: 123, name: '青山湖街道', parentId: 12 },
        ]
    },
];

// 租金范围
export const priceRanges = [
    { id: 1, name: '不限', min: 0, max: 999999 },
    { id: 2, name: '1000元以下', min: 0, max: 1000 },
    { id: 3, name: '1000-2000元', min: 1000, max: 2000 },
    { id: 4, name: '2000-3000元', min: 2000, max: 3000 },
    { id: 5, name: '3000-5000元', min: 3000, max: 5000 },
    { id: 6, name: '5000-8000元', min: 5000, max: 8000 },
    { id: 7, name: '8000元以上', min: 8000, max: 999999 },
    { id: 8, name: '自定义', min: 0, max: 0, custom: true }
];

// 户型选项
export const houseTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '一室' },
    { id: 3, name: '二室' },
    { id: 4, name: '三室' },
    { id: 5, name: '四室' },
    { id: 6, name: '四室以上' },
    { id: 7, name: '公寓' },
    { id: 8, name: '商住楼' },
    { id: 9, name: '别墅' }
];

// 房屋类型
export const propertyTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '普通住宅' },
    { id: 3, name: '公寓' },
    { id: 4, name: '别墅' },
    { id: 5, name: '农房' },
    { id: 6, name: '平房' },
    { id: 7, name: '四合院' },
];

// 租赁方式
export const rentTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '整租' },
    { id: 3, name: '合租' },
    { id: 4, name: '房东直租' },
    { id: 5, name: '中介租赁' }
];

// 装修情况
export const decorationTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '精装修' },
    { id: 3, name: '简装' },
    { id: 4, name: '毛坯' },
    { id: 5, name: '豪华装修' }
];

// 朝向选择
export const orientations = [
    { id: 1, name: '不限' },
    { id: 2, name: '东' },
    { id: 3, name: '南' },
    { id: 4, name: '西' },
    { id: 5, name: '北' },
    { id: 6, name: '东南' },
    { id: 7, name: '东北' },
    { id: 8, name: '西南' },
    { id: 9, name: '西北' },
    { id: 10, name: '南北通透' }
];

// 楼层
export const floorTypes = [
    { id: 1, name: '不限' },
    { id: 2, name: '低楼层', desc: '1-3层' },
    { id: 3, name: '中楼层', desc: '4-6层' },
    { id: 4, name: '高楼层', desc: '7层以上' },
];

// 房龄
export const houseAges = [
    { id: 1, name: '不限' },
    { id: 2, name: '5年以内' },
    { id: 3, name: '10年以内' },
    { id: 4, name: '15年以内' },
    { id: 5, name: '20年以内' },
    { id: 6, name: '20年以上' },
];

// 设施配置
export const facilities = [
    { id: 1, name: '无线网络', icon: 'i-carbon-wifi' },
    { id: 2, name: '电视', icon: 'i-carbon-tv' },
    { id: 3, name: '冰箱', icon: 'i-carbon-ice-vision' },
    { id: 4, name: '洗衣机', icon: 'i-carbon-machine-learning' },
    { id: 5, name: '空调', icon: 'i-carbon-air-conditioner' },
    { id: 6, name: '热水器', icon: 'i-carbon-temperature-hot' },
    { id: 7, name: '燃气灶', icon: 'i-carbon-fire' },
    { id: 8, name: '微波炉', icon: 'i-carbon-checkbox' },
    { id: 9, name: '油烟机', icon: 'i-carbon-map' },
    { id: 10, name: '电磁炉', icon: 'i-carbon-battery-charging' },
    { id: 11, name: '床', icon: 'i-carbon-sleeping' },
    { id: 12, name: '沙发', icon: 'i-carbon-template' },
    { id: 13, name: '衣柜', icon: 'i-carbon-folder' },
    { id: 14, name: '书桌', icon: 'i-carbon-desk' },
    { id: 15, name: '餐桌', icon: 'i-carbon-table' },
    { id: 16, name: '阳台', icon: 'i-carbon-sun' },
    { id: 17, name: '独卫', icon: 'i-carbon-toilet' },
    { id: 18, name: '车位', icon: 'i-carbon-car' }
];

// 租房租期
export const leaseTerms = [
    { id: 1, name: '不限' },
    { id: 2, name: '月租' },
    { id: 3, name: '1-3个月' },
    { id: 4, name: '4-6个月' },
    { id: 5, name: '7个月-1年' },
    { id: 6, name: '一年以上' },
];

// 房源特色标签
export const houseTags = [
    { id: 1, name: '近地铁', color: '#FF7D41' },
    { id: 2, name: '拎包入住', color: '#3B7CFF' },
    { id: 3, name: '押一付一', color: '#00B578' },
    { id: 4, name: '新上房源', color: '#F74F55' },
    { id: 5, name: '随时看房', color: '#6A36CA' },
    { id: 6, name: '可短租', color: '#FF7D41' },
    { id: 7, name: '带阳台', color: '#3B7CFF' },
    { id: 8, name: '独立卫生间', color: '#00B578' },
    { id: 9, name: '可养宠物', color: '#F74F55' },
    { id: 10, name: '首次出租', color: '#6A36CA' },
    { id: 11, name: '独立厨房', color: '#FF7D41' },
    { id: 12, name: '免押金', color: '#3B7CFF' },
    { id: 13, name: '南北通透', color: '#00B578' },
    { id: 14, name: '有电梯', color: '#F74F55' },
    { id: 15, name: '落地窗', color: '#6A36CA' },
];

// 支付方式
export const paymentMethods = [
    { id: 1, name: '不限' },
    { id: 2, name: '押一付一' },
    { id: 3, name: '押一付三' },
    { id: 4, name: '押二付一' },
    { id: 5, name: '押二付三' },
    { id: 6, name: '押一付六' },
    { id: 7, name: '半年付' },
    { id: 8, name: '年付' }
];

// 排序选项
export const sortOptions = [
    { id: 1, name: '默认排序' },
    { id: 2, name: '发布时间降序' },
    { id: 3, name: '价格升序' },
    { id: 4, name: '价格降序' },
    { id: 5, name: '面积升序' },
    { id: 6, name: '面积降序' }
];

// 标签选项
export const tagOptions = [
    { id: 1, name: '近地铁' },
    { id: 2, name: '精装修' },
    { id: 3, name: '押一付一' },
    { id: 4, name: '独卫' },
    { id: 5, name: '近商圈' },
    { id: 6, name: '近学校' },
    { id: 7, name: '安静' },
    { id: 8, name: '复式' },
    { id: 9, name: '南北通透' },
    { id: 10, name: '带阳台' },
    { id: 11, name: '可短租' }
];

// 租房攻略分类
export const rentalGuideCategories = [
    { id: 1, name: '新手租房' },
    { id: 2, name: '租房陷阱' },
    { id: 3, name: '合同签订' },
    { id: 4, name: '房屋维修' },
    { id: 5, name: '租客权益' },
];

// 模拟地铁线路数据
export const metroLines = [
    { id: 1, name: '1号线', color: '#D4237A' },
    { id: 2, name: '2号线', color: '#008B95' },
    { id: 3, name: '3号线', color: '#DE5203' },
    { id: 4, name: '4号线', color: '#008F3B' },
    { id: 5, name: '5号线', color: '#A13A94' },
    { id: 6, name: '6号线', color: '#8B5AAF' },
    { id: 7, name: '7号线', color: '#E5171D' },
    { id: 8, name: '8号线', color: '#0075C1' },
    { id: 9, name: '9号线', color: '#E2B656' },
    { id: 10, name: '10号线', color: '#B481BD' },
];

// 常用地点类型
export const placeTypes = [
    { id: 1, name: '地铁站', icon: 'i-carbon-train' },
    { id: 2, name: '学校', icon: 'i-carbon-education' },
    { id: 3, name: '医院', icon: 'i-carbon-hospital' },
    { id: 4, name: '商场', icon: 'i-carbon-shopping-bag' },
    { id: 5, name: '写字楼', icon: 'i-carbon-building' },
    { id: 6, name: '公园', icon: 'i-carbon-tree' },
    { id: 7, name: '银行', icon: 'i-carbon-money' },
];

// 房源配置图标
export const houseIcons = {
    area: 'i-carbon-area',
    bedroom: 'i-carbon-sleeping',
    bathroom: 'i-carbon-bath',
    livingroom: 'i-carbon-sofa',
    orientation: 'i-carbon-compass',
    floor: 'i-carbon-building',
};

// 模拟热门搜索
export const hotSearches = [
    '三室两厅',
    '押一付一',
    '精装修',
    '西湖边',
    '地铁口',
    '2000元以下',
    '可养宠物',
    '拎包入住'
];

// 模拟历史搜索
export const historySearches = [
    '城西银泰附近',
    '单身公寓',
    '西湖区两室',
    '地铁1号线',
    '朝南主卧',
    '武林广场附近'
]; 
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null,
    accessToken: null,
  }),
  actions: {
    // async login(username: string, password: string) {
    //   const res = await http.post('/login', { username, password })
    //   this.user = res.data
    // },
    clearUserInfo() {
      this.user = null
      this.accessToken = null
    },
    setUserInfo(user: any, accessToken: string) {
      this.user = user
      this.accessToken = accessToken
    },
  },
  getters: {
    getUser: (state) => state.user,
  },
})
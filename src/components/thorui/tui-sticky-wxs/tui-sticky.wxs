var isH5 = false;
if (typeof window === 'object') isH5 = true;
var stickyChange = function(scrollTop, oldScrollTop, ownerInstance, ins) {
	if (!oldScrollTop && scrollTop === 0) return false;
	var dataset = ins.getDataset()
	var top = +dataset.top;
	var height = +dataset.height;
	var stickyTop = +dataset.stickytop;
	var isNativeHeader = dataset.isnativeheader;
	var isFixed = false;
	var distance = stickyTop
	if (isNativeHeader && isH5) {
		distance = distance - 44
		distance = distance < 0 ? 0 : distance
	}
	if (dataset.container) {
		isFixed = (scrollTop + distance >= top && scrollTop + distance < top + height) ? true : false
	} else {
		isFixed = scrollTop + distance >= top ? true : false
	}
	var tsb = ownerInstance.selectComponent('.tui-sticky-bar')
	var tss = ownerInstance.selectComponent('.tui-sticky-seat')
	if (!tsb || !tss) return;
	if (isFixed) {
		tsb.setStyle({
			"top": stickyTop + 'px'
		}).addClass('tui-sticky-fixed')
		tss.setStyle({
			"display": 'block'
		})
	} else {
		tsb.setStyle({
			"top": 'auto'
		}).removeClass('tui-sticky-fixed')
		tss.setStyle({
			"display": 'none'
		})
	}
	ownerInstance.triggerEvent("sticky", [{
		isFixed: isFixed,
		index: parseInt(dataset.index)
	}])
}

module.exports = {
	stickyChange: stickyChange
}
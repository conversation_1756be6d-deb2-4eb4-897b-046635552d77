<template>
  <view class="empty-result flex flex-col items-center justify-center my-80rpx">
    <text :class="[icon, 'text-100rpx color-grey-light']"></text>
    <text class="empty-message text-32rpx color-info mt-30rpx">{{
      message
    }}</text>
    <text
      v-if="subMessage"
      class="empty-sub-message text-26rpx color-grey mt-10rpx"
      >{{ subMessage }}</text
    >
    <slot name="action">
      <view v-if="actionText" class="action-btn mt-40rpx" @tap="handleAction">{{
        actionText
      }}</view>
    </slot>
  </view>
</template>

<script setup lang="ts">
defineProps({
  message: {
    type: String,
    default: "暂无数据",
  },
  subMessage: {
    type: String,
    default: "",
  },
  icon: {
    type: String,
    default: "i-carbon-data-error",
  },
  actionText: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["action"]);

const handleAction = () => {
  emit("action");
};
</script>

<style lang="scss" scoped>
.empty-result {
  width: 100%;
  height: 400rpx;
}

.color-grey-light {
  color: #ddd;
}

.color-info {
  color: #666;
}

.color-grey {
  color: #999;
}

.action-btn {
  padding: 12rpx 40rpx;
  background-color: rgba($primary, 0.1);
  color: $primary;
  border-radius: 40rpx;
  font-size: 28rpx;
}
</style>

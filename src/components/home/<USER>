<template>
  <view class="recommend-container pb-100rpx">
    <!-- 广告轮播图 -->
    <view v-if="showBanner" class="ad-banner-container px-20rpx">
      <swiper
        class="ad-banner-swiper"
        :indicator-dots="true"
        indicator-type="expand"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="#ff6d00"
      >
        <swiper-item v-for="(item, index) in adBannerList" :key="index">
          <image :src="item.image" mode="aspectFill" class="ad-banner-image" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 快捷功能图标区 -->
    <view class="icons-container bg-white px-20rpx py-20rpx mb-20rpx">
      <view class="grid grid-cols-5 gap-20rpx">
        <view
          v-for="(icon, index) in quickIcons"
          :key="index"
          class="icon-item flex flex-col items-center"
        >
          <view class="icon-circle mb-10rpx" :class="icon.bgColor">
            <text :class="icon.icon"></text>
          </view>
          <text class="text-24rpx color-info">{{ icon.name }}</text>
          <view v-if="icon.isNew" class="new-tag absolute top-0 right-10rpx"
            >NEW</view
          >
        </view>
      </view>
    </view>

    <!-- 今日必抢 -->
    <view class="must-buy-container bg-white mb-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">今日必抢</text>
        <view class="flex items-center">
          <text class="text-26rpx color-grey">更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>
      <scroll-view scroll-x class="must-buy-scroll">
        <view class="flex px-20rpx pb-20rpx">
          <view
            v-for="(item, index) in mustBuyItems"
            :key="index"
            class="must-buy-item mr-20rpx rounded-lg overflow-hidden"
          >
            <image :src="item.image" mode="aspectFill" class="item-image" />
            <view class="item-info p-10rpx">
              <text class="item-title text-28rpx color-main line-clamp-2">{{
                item.title
              }}</text>
              <view class="flex justify-between items-center mt-10rpx">
                <view class="flex items-baseline">
                  <text class="text-orange-500 text-32rpx font-bold"
                    >¥{{ item.price }}</text
                  >
                  <text class="color-grey text-24rpx line-through ml-10rpx"
                    >¥{{ item.originalPrice }}</text
                  >
                </view>
                <view class="buy-btn">抢</view>
              </view>
              <text class="color-grey text-24rpx"
                >已抢{{ item.soldCount }}</text
              >
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 附近抢购 -->
    <view class="nearby-container bg-white">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">附近抢购</text>
        <view class="flex items-center">
          <text class="text-26rpx color-grey">更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <!-- 分类标签 -->
      <scroll-view scroll-x class="category-tabs" show-scrollbar="false">
        <view class="flex py-10rpx px-20rpx">
          <view
            v-for="(category, index) in categories"
            :key="index"
            class="category-item px-20rpx py-8rpx mr-20rpx rounded-full"
            :class="
              category.active
                ? 'bg-orange-100 text-orange-500'
                : 'bg-gray-100 color-info'
            "
          >
            {{ category.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 附近商品列表 -->
      <view class="nearby-list px-20rpx pb-20rpx">
        <view
          v-for="(item, index) in nearbyItems"
          :key="index"
          class="nearby-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
        >
          <image
            :src="item.image"
            mode="aspectFill"
            class="nearby-image rounded-lg"
          />
          <view class="nearby-info flex-1 ml-20rpx">
            <view class="flex items-center">
              <text class="nearby-title text-30rpx font-bold line-clamp-2">{{
                item.title
              }}</text>
              <text v-if="item.adTag" class="ad-tag ml-10rpx">广告</text>
            </view>
            <text class="color-grey text-26rpx">距离{{ item.distance }}</text>
            <view class="flex items-center mt-10rpx">
              <view class="flex items-baseline">
                <text class="text-orange-500 text-32rpx font-bold"
                  >¥{{ item.price }}</text
                >
                <text class="color-grey text-24rpx line-through ml-10rpx"
                  >¥{{ item.originalPrice }}</text
                >
              </view>
              <view v-if="item.memberDiscount" class="member-tag ml-10rpx"
                >会员减{{ item.memberDiscount }}</view
              >
            </view>
            <view class="flex justify-between mt-10rpx">
              <text class="color-grey text-24rpx"
                >{{ item.peopleCount }}人使用</text
              >
              <view v-if="item.rating" class="rating text-orange-500"
                >{{ item.rating }}分</view
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps } from "vue";

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 广告轮播图数据
const adBannerList = [
  {
    image: "https://picsum.photos/seed/ad1/700/238",
    link: "https://example.com/ad1",
  },
  {
    image: "https://picsum.photos/seed/ad2/700/238",
    link: "https://example.com/ad2",
  },
  {
    image: "https://picsum.photos/seed/ad3/700/238",
    link: "https://example.com/ad3",
  },
];

// 快捷功能图标
const quickIcons = [
  { name: "今日主题", icon: "i-carbon-thumbs-up", bgColor: "bg-red-500" },
  { name: "免费优惠", icon: "i-carbon-store", bgColor: "bg-green-500" },
  { name: "配送卡", icon: "i-carbon-favorite", bgColor: "bg-yellow-500" },
  { name: "邀请好友", icon: "i-carbon-user-multiple", bgColor: "bg-blue-500" },
  {
    name: "商户入驻",
    icon: "i-carbon-home",
    bgColor: "bg-purple-500",
    isNew: true,
  },
];

// 今日必抢商品
const mustBuyItems = [
  {
    image: "https://picsum.photos/seed/must-buy-1/300/200",
    title: "卡夫卡少儿免费公开课",
    price: "29",
    originalPrice: "59",
    soldCount: "1000+",
  },
  {
    image: "https://picsum.photos/seed/must-buy-2/300/200",
    title: "天然棉纱影婚情侣套餐",
    price: "9.9",
    originalPrice: "99",
    soldCount: "1000+",
  },
];

// 分类标签
const categories = [
  { name: "人气推荐", active: true },
  { name: "最新上架", active: false },
  { name: "优惠特惠", active: false },
  { name: "好评优先", active: false },
];

// 附近抢购商品
const nearbyItems = [
  {
    image: "https://picsum.photos/seed/nearby-1/200/150",
    title: "3天2晚包食宿 云南大理-洱海-情侣双人套餐",
    distance: "1.2km",
    price: "299",
    originalPrice: "1299",
    memberDiscount: "¥100",
    peopleCount: "200+",
    adTag: true,
  },
  {
    image: "https://picsum.photos/seed/nearby-2/200/150",
    title: "卡夫卡少儿45分钟舞蹈免费公开课",
    distance: "3.3km",
    price: "29.9",
    originalPrice: "49.9",
    peopleCount: "100+",
    adTag: true,
    rating: "5.0",
  },
  {
    image: "https://picsum.photos/seed/nearby-3/200/150",
    title: "简约大气风-浪漫沙滩婚纱摄影套餐",
    distance: "3.3km",
    price: "1229.9",
    originalPrice: "4999",
    memberDiscount: "¥299",
    peopleCount: "302",
    rating: "5.0",
  },
];
</script>

<style lang="scss" scoped>
.recommend-container {
}

.ad-banner-container {
  margin-bottom: 20rpx;
}

.ad-banner-swiper {
  height: 238rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-banner-image {
  width: 100%;
  height: 238rpx;
}

.icon-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 40rpx;
}

.icon-item {
  position: relative;
}

.new-tag {
  background-color: $primary;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
}

.must-buy-scroll {
  white-space: nowrap;
}

.must-buy-item {
  width: 300rpx;
  display: inline-block;
  background-color: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-image {
  width: 300rpx;
  height: 200rpx;
}

.item-title {
  height: 80rpx;
}

.buy-btn {
  width: 40rpx;
  height: 40rpx;
  background-color: $primary;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
}

.category-item {
  font-size: 24rpx;
}

.nearby-image {
  width: 200rpx;
  height: 150rpx;
}

.ad-tag {
  font-size: 20rpx;
  color: #999;
  border: 1rpx solid #ddd;
  padding: 0 6rpx;
  border-radius: 4rpx;
}

.member-tag {
  font-size: 20rpx;
  color: $primary;
  background-color: #fff5e6;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}
</style>

<template>
  <view
    class="filter-section px-30rpx py-20rpx"
    :class="{ 'pb-120rpx': isLastSection }"
  >
    <view class="filter-section-title mb-20rpx text-30rpx font-bold">
      {{ title }}
    </view>
    <view class="filter-options flex flex-wrap">
      <view
        v-for="(option, index) in options"
        :key="index"
        class="filter-option-item"
        :class="{ 'filter-option-active': option.active }"
        @tap="handleOptionTap(index)"
      >
        {{ option.name }}
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  // 选项标题
  title: {
    type: String,
    required: true,
  },
  // 筛选选项数据
  options: {
    type: Array,
    required: true,
  },
  // 筛选类型
  type: {
    type: String,
    required: true,
  },
  // 是否是最后一个部分（添加底部间距）
  isLastSection: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["option-change"]);

// 处理选项点击，使用tap事件提高响应速度
const handleOptionTap = (index) => {
  emit("option-change", { type: props.type, index });
};
</script>

<style lang="scss" scoped>
.filter-section {
  border-bottom: 1rpx solid #f5f5f5;
}

.filter-section-title {
  color: #333;
}

.filter-options {
  margin-bottom: 10rpx;
}

.filter-option-item {
  padding: 12rpx 24rpx;
  background-color: $bg-color;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
  transition: all 0.2s ease;
}

.filter-option-active {
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  font-weight: 500;
}
</style>

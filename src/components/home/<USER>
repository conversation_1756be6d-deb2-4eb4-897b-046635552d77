<template>
  <view class="dating-container pb-100rpx">
    <!-- 广告轮播图 -->
    <view v-if="showBanner" class="ad-banner-container px-20rpx">
      <swiper
        class="ad-banner-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(255, 255, 255, 0.6)"
        indicator-active-color="$primary"
      >
        <swiper-item v-for="(item, index) in adBannerList" :key="index">
          <image :src="item.image" mode="aspectFill" class="ad-banner-image" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 相亲活动 -->
    <view class="activities bg-white px-20rpx py-30rpx mb-20rpx">
      <view class="section-title mb-20rpx">
        <text class="text-32rpx font-bold">相亲活动</text>
      </view>
      <scroll-view scroll-x class="activities-scroll">
        <view class="flex">
          <view
            v-for="(activity, index) in datingActivities"
            :key="index"
            class="activity-item mr-20rpx"
          >
            <image
              :src="activity.image"
              mode="aspectFill"
              class="activity-image rounded-lg"
            />
            <view class="p-10rpx">
              <text class="activity-title text-28rpx font-bold line-clamp-1">{{
                activity.title
              }}</text>
              <view class="flex justify-between items-center mt-10rpx">
                <text class="time text-24rpx color-grey">{{
                  activity.time
                }}</text>
                <text class="activity-tag">{{ activity.type }}</text>
              </view>
              <view class="flex justify-between items-center mt-10rpx">
                <view class="flex items-baseline">
                  <text class="price text-theme text-30rpx font-bold"
                    >¥{{ activity.price }}</text
                  >
                  <text
                    v-if="activity.originalPrice"
                    class="original-price color-grey text-24rpx line-through ml-10rpx"
                    >¥{{ activity.originalPrice }}</text
                  >
                </view>
                <text class="people-count text-24rpx color-grey"
                  >{{ activity.peopleCount }}人</text
                >
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 筛选区域 -->
    <view class="filter-container bg-white mb-20rpx">
      <view class="flex px-20rpx py-20rpx border-b border-gray-100">
        <view
          class="flex-1 flex justify-center items-center"
          :class="{ 'active-filter': activeFilter === 'all' }"
          @click="setFilter('all')"
        >
          <text>全部</text>
        </view>
        <view
          class="flex-1 flex justify-center items-center"
          :class="{ 'active-filter': activeFilter === 'male' }"
          @click="setFilter('male')"
        >
          <text>找女友</text>
        </view>
        <view
          class="flex-1 flex justify-center items-center"
          :class="{ 'active-filter': activeFilter === 'female' }"
          @click="setFilter('female')"
        >
          <text>找男友</text>
        </view>
        <view
          class="flex-1 flex justify-center items-center"
          :class="{ 'active-filter': activeFilter === 'verified' }"
          @click="setFilter('verified')"
        >
          <text>已认证</text>
        </view>
      </view>
      <view
        class="advanced-filter flex px-20rpx py-10rpx justify-between items-center"
      >
        <view class="filter-option flex items-center">
          <text class="text-28rpx">年龄</text>
          <text class="i-carbon-chevron-down ml-5rpx color-grey"></text>
        </view>
        <view class="filter-option flex items-center">
          <text class="text-28rpx">学历</text>
          <text class="i-carbon-chevron-down ml-5rpx color-grey"></text>
        </view>
        <view class="filter-option flex items-center">
          <text class="text-28rpx">收入</text>
          <text class="i-carbon-chevron-down ml-5rpx color-grey"></text>
        </view>
        <view class="filter-option flex items-center">
          <text class="text-28rpx">更多</text>
          <text class="i-carbon-chevron-down ml-5rpx color-grey"></text>
        </view>
      </view>
    </view>

    <!-- 推荐用户列表 -->
    <view class="user-list px-20rpx">
      <view class="grid grid-cols-2 gap-20rpx">
        <view
          v-for="(user, index) in recommendUsers"
          :key="index"
          class="user-card bg-white rounded-lg overflow-hidden shadow-sm"
        >
          <view class="relative">
            <image :src="user.avatar" mode="aspectFill" class="user-avatar" />
            <view v-if="user.isVip" class="vip-badge">
              <text class="i-carbon-star-filled text-yellow-400"></text>
            </view>
            <view v-if="user.isVerified" class="verified-badge"> 已认证 </view>
          </view>
          <view class="user-info p-15rpx">
            <view class="flex items-center justify-between">
              <text class="user-name text-30rpx font-bold">{{
                user.name
              }}</text>
              <text class="user-age text-26rpx">{{ user.age }}岁</text>
            </view>
            <view class="user-detail mt-5rpx flex items-center justify-between">
              <text class="color-grey text-24rpx">{{ user.job }}</text>
              <text class="color-grey text-24rpx">{{ user.education }}</text>
            </view>
            <view class="user-tags flex flex-wrap mt-10rpx">
              <view
                v-for="(tag, tagIndex) in user.tags"
                :key="tagIndex"
                class="user-tag"
              >
                {{ tag }}
              </view>
            </view>
            <view class="flex justify-between items-center mt-10rpx">
              <text class="location color-grey text-24rpx">{{
                user.location
              }}</text>
              <view class="like-btn flex items-center">
                <text
                  class="i-carbon-favorite text-24rpx"
                  :class="user.isLiked ? 'text-theme' : 'color-grey'"
                ></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 约会攻略 -->
    <view class="dating-guides bg-white mt-20rpx">
      <view
        class="section-header flex justify-between items-center px-20rpx py-20rpx"
      >
        <text class="text-32rpx font-bold">约会攻略</text>
        <view class="flex items-center">
          <text class="text-26rpx color-grey">更多</text>
          <text class="i-carbon-chevron-right color-grey"></text>
        </view>
      </view>

      <view class="guides-list px-20rpx pb-20rpx">
        <view
          v-for="(guide, index) in datingGuides"
          :key="index"
          class="guide-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
        >
          <image
            :src="guide.image"
            mode="aspectFill"
            class="guide-image rounded-lg"
          />
          <view class="guide-info flex-1 ml-20rpx">
            <text class="guide-title text-30rpx font-bold line-clamp-2">{{
              guide.title
            }}</text>
            <text
              class="guide-desc text-26rpx color-grey line-clamp-2 mt-10rpx"
              >{{ guide.description }}</text
            >
            <view class="flex justify-between items-center mt-10rpx">
              <text class="author text-24rpx color-grey">{{
                guide.author
              }}</text>
              <view class="flex items-center">
                <text class="i-carbon-thumbs-up color-grey mr-5rpx"></text>
                <text class="like-count text-24rpx color-grey">{{
                  guide.likeCount
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, defineProps } from "vue";

// 定义props接收是否显示广告banner
const props = defineProps({
  showBanner: {
    type: Boolean,
    default: true,
  },
});

// 广告轮播图数据
const adBannerList = [
  {
    image: "/static/images/dating-ad-banner1.jpg",
    link: "https://example.com/dating/ad1",
  },
  {
    image: "/static/images/dating-ad-banner2.jpg",
    link: "https://example.com/dating/ad2",
  },
  {
    image: "/static/images/dating-ad-banner3.jpg",
    link: "https://example.com/dating/ad3",
  },
];

// 相亲活动
const datingActivities = [
  {
    image: "/static/images/dating-activity1.jpg",
    title: "周末相约·高质量单身交友派对",
    time: "本周六 14:00-17:00",
    type: "线下",
    price: "168",
    originalPrice: "298",
    peopleCount: "32/50",
  },
  {
    image: "/static/images/dating-activity2.jpg",
    title: "户外徒步·遇见心动的TA",
    time: "下周日 09:00-16:00",
    type: "户外",
    price: "128",
    originalPrice: "198",
    peopleCount: "28/40",
  },
  {
    image: "/static/images/dating-activity3.jpg",
    title: "烘焙甜点·甜蜜邂逅",
    time: "本周日 13:30-16:30",
    type: "室内",
    price: "198",
    originalPrice: "268",
    peopleCount: "18/24",
  },
];

// 筛选条件
const activeFilter = ref("all");
const setFilter = (filter: string) => {
  activeFilter.value = filter;
};

// 推荐用户
const recommendUsers = [
  {
    avatar: "/static/images/user1.jpg",
    name: "小林",
    age: 28,
    job: "产品经理",
    education: "硕士",
    tags: ["旅行", "摄影", "美食"],
    location: "朝阳区·2.5km",
    isVip: true,
    isVerified: true,
    isLiked: false,
  },
  {
    avatar: "/static/images/user2.jpg",
    name: "安妮",
    age: 26,
    job: "财务",
    education: "本科",
    tags: ["电影", "音乐", "健身"],
    location: "海淀区·3.8km",
    isVip: false,
    isVerified: true,
    isLiked: true,
  },
  {
    avatar: "/static/images/user3.jpg",
    name: "小王",
    age: 29,
    job: "设计师",
    education: "本科",
    tags: ["插画", "动漫", "宅家"],
    location: "西城区·5.2km",
    isVip: true,
    isVerified: false,
    isLiked: false,
  },
  {
    avatar: "/static/images/user4.jpg",
    name: "小美",
    age: 25,
    job: "教师",
    education: "本科",
    tags: ["阅读", "瑜伽", "烘焙"],
    location: "东城区·4.1km",
    isVip: false,
    isVerified: true,
    isLiked: false,
  },
];

// 约会攻略
const datingGuides = [
  {
    image: "/static/images/guide-date1.jpg",
    title: "第一次约会聊什么？教你避免尴尬冷场",
    description: "约会聊天话题技巧，让你轻松拉近两人距离，增进感情...",
    author: "情感顾问",
    likeCount: "2.5k",
  },
  {
    image: "/static/images/guide-date2.jpg",
    title: "脱单秘籍：如何提升自己的魅力值",
    description:
      "从外表、谈吐到内在修养，全方位提升个人魅力，让你更容易获得异性青睐...",
    author: "形象顾问",
    likeCount: "3.2k",
  },
];
</script>

<style lang="scss" scoped>
.dating-container {
}

.ad-banner-container {
  margin-bottom: 20rpx;
}

.ad-banner-swiper {
  height: 238rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.ad-banner-image {
  width: 100%;
  height: 238rpx;
}

.activities-scroll {
  white-space: nowrap;
}

.activity-item {
  width: 300rpx;
  display: inline-block;
  background-color: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-image {
  width: 300rpx;
  height: 180rpx;
}

.activity-tag {
  font-size: 20rpx;
  color: $primary;
  background-color: #fff5e6;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.active-filter {
  color: $primary;
  font-weight: bold;
  position: relative;
}

.active-filter::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: $primary;
  border-radius: 4rpx;
}

.filter-option {
  padding: 6rpx 15rpx;

  border-radius: 20rpx;
}

.user-avatar {
  width: 100%;
  height: 300rpx;
}

.vip-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.verified-badge {
  position: absolute;
  bottom: 10rpx;
  left: 10rpx;
  background-color: $primary;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

.text-theme {
  color: $primary;
}

.user-tag {
  font-size: 22rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
}

.guide-image {
  width: 200rpx;
  height: 150rpx;
}
</style>

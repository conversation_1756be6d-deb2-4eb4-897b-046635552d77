<template>
  <view
    class="post-item bg-white mx-20rpx my-20rpx rounded-lg p-30rpx"
    @tap="onItemClick"
  >
    <!-- 用户信息 -->
    <view class="user-info flex items-center mb-20rpx">
      <image-loader :src="post.avatar" class="user-avatar"></image-loader>
      <view class="ml-20rpx flex-1">
        <view class="text-30rpx font-bold">{{ post.username }}</view>
        <view class="text-24rpx color-grey">
          {{ post.position }} · {{ post.company }}
          <text v-if="post.location" class="ml-10rpx"
            >· {{ post.location }}</text
          >
        </view>
      </view>
      <view class="follow-btn" v-if="!post.isFollowing" @tap.stop="onFollow">
        <text class="text-26rpx">关注</text>
      </view>
      <view class="following-btn" v-else @tap.stop="onUnfollow">
        <text class="text-26rpx">已关注</text>
      </view>
    </view>

    <!-- 动态内容 -->
    <view class="post-content mb-20rpx">
      <view class="text-28rpx color-main line-height-40rpx mb-20rpx">
        {{ post.content }}
      </view>

      <!-- 图片展示 -->
      <view
        class="post-images flex flex-wrap"
        v-if="post.images && post.images.length > 0"
      >
        <view
          v-for="(img, imgIndex) in post.images"
          :key="imgIndex"
          class="post-image-wrapper"
        >
          <image-loader
            :src="img"
            class="post-image"
            mode="aspectFill"
            @tap.stop="onImagePreview(post.images, imgIndex)"
          ></image-loader>
        </view>
      </view>

      <!-- 视频展示 -->
      <view class="post-video-container" v-if="post.video">
        <video
          :src="post.video"
          class="post-video"
          :poster="post.videoCover"
          object-fit="cover"
          @error="onVideoError"
        ></video>
      </view>
    </view>

    <!-- 互动区域 -->
    <view class="post-actions flex border-top pt-20rpx">
      <view class="action-item flex items-center flex-1" @tap.stop="onLike">
        <text
          :class="
            post.isLiked
              ? 'i-carbon-thumbs-up-filled text-primary'
              : 'i-carbon-thumbs-up color-grey'
          "
          class="text-36rpx"
        ></text>
        <text
          class="ml-10rpx text-26rpx"
          :class="post.isLiked ? 'text-primary' : 'color-grey'"
        >
          {{ post.likes }}
        </text>
      </view>
      <view class="action-item flex items-center flex-1" @tap.stop="onComment">
        <text class="i-carbon-chat color-grey text-36rpx"></text>
        <text class="ml-10rpx text-26rpx color-grey">{{ post.comments }}</text>
      </view>
      <view class="action-item flex items-center flex-1" @tap.stop="onShare">
        <text class="i-carbon-share color-grey text-36rpx"></text>
        <text class="ml-10rpx text-26rpx color-grey">分享</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
defineProps({
  post: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits([
  "follow",
  "unfollow",
  "like",
  "comment",
  "share",
  "preview-image",
  "click",
]);

// 点击整个帖子
const onItemClick = () => {
  emit("click");
};

// 点赞
const onLike = () => {
  emit("like");
};

// 评论
const onComment = () => {
  emit("comment");
};

// 分享
const onShare = () => {
  emit("share");
};

// 关注
const onFollow = () => {
  emit("follow");
};

// 取消关注
const onUnfollow = () => {
  emit("unfollow");
};

// 图片预览
const onImagePreview = (images: string[], current: number) => {
  emit("preview-image", images, current);
};

// 视频加载错误
const onVideoError = () => {
  uni.showToast({
    title: "视频加载失败",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.follow-btn {
  padding: 6rpx 20rpx;
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  border-radius: 30rpx;
  border: 1rpx solid $primary;
}

.following-btn {
  padding: 6rpx 20rpx;
  color: #999;
  border-radius: 30rpx;
  border: 1rpx solid #ddd;
}

.post-images {
  margin: 0 -10rpx;
}

.post-image-wrapper {
  width: calc(33.33% - 20rpx);
  height: 200rpx;
  margin: 10rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.post-image {
  width: 100%;
  height: 100%;
}

.post-video-container {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.post-video {
  width: 100%;
  height: 100%;
}

.border-top {
  border-top: 1rpx solid #f0f0f0;
}

.pt-20rpx {
  padding-top: 20rpx;
}

.line-height-40rpx {
  line-height: 40rpx;
}

.text-primary {
  color: $primary;
}
</style>

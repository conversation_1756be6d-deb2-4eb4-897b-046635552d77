<template>
  <view class="image-loader" :style="{ width, height }">
    <!-- 加载占位符 -->
    <view v-if="isLoading" class="placeholder loading-placeholder">
      <view class="loading-icon">
        <text class="i-carbon-image color-placeholder text-48rpx"></text>
      </view>
    </view>

    <!-- 错误占位符 -->
    <view v-if="loadError" class="placeholder error-placeholder">
      <view class="error-icon">
        <text class="i-carbon-warning-alt color-grey text-48rpx"></text>
      </view>
    </view>

    <!-- 实际图片，使用懒加载方式 -->
    <image
      v-if="src"
      :src="src"
      :lazy-load="true"
      :mode="mode"
      :style="{ opacity: isLoaded ? 1 : 0 }"
      class="actual-image"
      @load="onImageLoad"
      @error="onImageError"
    ></image>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const props = defineProps({
  src: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
  mode: {
    type: String,
    default: "aspectFill",
  },
  defaultImage: {
    type: String,
    default: "",
  },
});

const isLoading = ref(true);
const loadError = ref(false);
const isLoaded = ref(false);

// 图片加载成功
const onImageLoad = () => {
  isLoading.value = false;
  isLoaded.value = true;
};

// 图片加载失败
const onImageError = () => {
  isLoading.value = false;
  loadError.value = true;
};
</script>

<style lang="scss" scoped>
.image-loader {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: inherit;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.loading-placeholder {
  background-color: #f9f9f9;
}

.error-placeholder {
  background-color: #f2f2f2;
}

.actual-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  transition: opacity 0.3s ease;
}
</style>

<template>
  <view class="job-category-selector">
    <!-- 搜索框 -->
    <view class="search-container py-20rpx px-30rpx bg-white">
      <view
        class="search-box flex items-center bg-gray-100 px-20rpx py-12rpx rounded-full"
      >
        <text class="i-carbon-search mr-10rpx text-gray-400"></text>
        <input
          class="flex-1 text-28rpx"
          placeholder="搜索职位名称"
          v-model="searchText"
          @input="handleSearch"
          confirm-type="search"
        />
      </view>
    </view>

    <!-- 类别选择区 -->
    <view class="category-container flex">
      <!-- 左侧行业分类列表 -->
      <scroll-view class="industry-list" scroll-y>
        <view
          v-for="(category, index) in jobCategories"
          :key="category.id"
          :class="[
            'industry-item py-30rpx px-20rpx',
            activeIndustryIndex === index ? 'active-industry' : '',
          ]"
          @tap="selectIndustry(index)"
        >
          <text class="text-28rpx">{{ category.name }}</text>
        </view>
      </scroll-view>

      <!-- 右侧职位列表 -->
      <scroll-view class="job-list" scroll-y>
        <view class="px-20rpx py-20rpx" v-if="showSearchResults">
          <view class="job-group">
            <view
              v-for="job in searchResults"
              :key="job.id"
              class="job-item py-25rpx"
              :class="{ 'selected-job': isJobSelected(job) }"
              @tap="selectJob(job)"
            >
              <text class="text-28rpx">{{ job.name }}</text>
            </view>
          </view>
        </view>
        <view class="px-20rpx py-20rpx" v-else>
          <view class="job-group">
            <view
              v-for="job in currentJobs"
              :key="job.id"
              class="job-item py-25rpx"
              :class="{ 'selected-job': isJobSelected(job) }"
              @tap="selectJob(job)"
            >
              <text class="text-28rpx">{{ job.name }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 已选职位展示 -->
    <view
      class="selected-container px-30rpx py-20rpx bg-white"
      v-if="modelValue"
    >
      <view class="flex items-center">
        <text class="text-26rpx text-gray-500 mr-20rpx">已选：</text>
        <view
          class="selected-tag flex items-center bg-gray-100 rounded-full px-20rpx py-10rpx"
        >
          <text class="text-26rpx text-primary">{{ modelValue }}</text>
          <text
            class="i-carbon-close ml-10rpx text-24rpx"
            @tap="clearSelection"
          ></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { jobCategories } from "@/utils/jobData";

interface JobItem {
  id: number;
  name: string;
  parentCategory?: string;
}

const props = defineProps({
  modelValue: String, // v-model绑定值
  categoryId: Number, // 职位类别ID
});

const emit = defineEmits(["update:modelValue", "update:categoryId", "change"]);

// 激活的行业索引
const activeIndustryIndex = ref(0);

// 搜索文本
const searchText = ref("");
const showSearchResults = ref(false);

// 当前选中的职位
const selectedJob = ref<JobItem | null>(null);

// 当前行业下的职位列表
const currentJobs = computed(() => {
  return jobCategories[activeIndustryIndex.value]?.subCategories || [];
});

// 所有职位的平铺列表（用于搜索）
const allJobs = computed(() => {
  const jobs: JobItem[] = [];
  jobCategories.forEach((category) => {
    category.subCategories.forEach((job) => {
      jobs.push({
        ...job,
        parentCategory: category.name,
      });
    });
  });
  return jobs;
});

// 搜索结果
const searchResults = ref<JobItem[]>([]);

// 选择行业
const selectIndustry = (index: number) => {
  activeIndustryIndex.value = index;
  searchText.value = "";
  showSearchResults.value = false;
};

// 选择职位
const selectJob = (job: JobItem) => {
  // 如果是搜索结果中的职位，找到对应的行业
  if (showSearchResults.value) {
    for (let i = 0; i < jobCategories.length; i++) {
      const found = jobCategories[i].subCategories.find(
        (item) => item.id === job.id
      );
      if (found) {
        activeIndustryIndex.value = i;
        break;
      }
    }
  }

  selectedJob.value = job;
  emit(
    "update:modelValue",
    `${job.parentCategory || jobCategories[activeIndustryIndex.value].name}-${
      job.name
    }`
  );
  emit("update:categoryId", job.id);
  emit("change", {
    id: job.id,
    name: job.name,
    categoryName:
      job.parentCategory || jobCategories[activeIndustryIndex.value].name,
    fullName: `${
      job.parentCategory || jobCategories[activeIndustryIndex.value].name
    }-${job.name}`,
  });
};

// 搜索处理
const handleSearch = () => {
  if (!searchText.value) {
    showSearchResults.value = false;
    return;
  }

  showSearchResults.value = true;
  const keyword = searchText.value.toLowerCase();
  searchResults.value = allJobs.value.filter((job) =>
    job.name.toLowerCase().includes(keyword)
  );
};

// 判断职位是否被选中
const isJobSelected = (job: JobItem) => {
  if (!props.modelValue) return false;

  const fullJobName = `${
    job.parentCategory || jobCategories[activeIndustryIndex.value].name
  }-${job.name}`;
  return props.modelValue === fullJobName;
};

// 清除选择
const clearSelection = () => {
  selectedJob.value = null;
  emit("update:modelValue", "");
  emit("update:categoryId", 0);
  emit("change", null);
};

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      selectedJob.value = null;
    }
  }
);
</script>

<style lang="scss" scoped>
.job-category-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.category-container {
  flex: 1;
  height: calc(100vh - 240rpx);
  overflow: hidden;
}

.industry-list {
  width: 35%;
  height: 100%;
  background-color: #f6f6f6;
}

.industry-item {
  border-left: 4rpx solid transparent;
}

.active-industry {
  background-color: #fff;
  border-left-color: #ff6d00;
  color: #ff6d00;
  font-weight: 500;
}

.job-list {
  width: 65%;
  height: 100%;
  background-color: #fff;
}

.job-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.job-item:last-child {
  border-bottom: none;
}

.selected-job {
  color: #ff6d00;
}

.selected-tag {
  max-width: 80%;
}

.text-primary {
  color: #ff6d00;
}
</style>

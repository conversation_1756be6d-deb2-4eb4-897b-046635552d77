<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
onLaunch(() => {
  console.log("App Launch");
});
onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>

<style lang="scss">
/* 全局CSS变量，从scss变量转换过来 */
// :root {
//   --primary: #{$primary};
//   --text-main: #{$text-main};
//   --text-info: #{$text-info};
//   --text-inverse: #{$text-inverse};
//   --text-grey: #{$text-grey};
//   --text-placeholder: #{$text-placeholder};
//   --text-disable: #{$text-disable};
//   --bg: #{$bg};
// }

page,
uni-page-body {
  width: 100%;
  height: 100%;
  background-color: $bg-color;
}
</style>

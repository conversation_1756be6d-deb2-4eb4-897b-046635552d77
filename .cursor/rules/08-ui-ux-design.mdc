---
description: 
globs: 
alwaysApply: true
---
# UI/UX设计规范

## 设计语言

项目采用简洁、现代的设计语言，生成高保真的页面UI，以提供良好的用户体验。

## 色彩系统

### 主色调

项目使用橙色作为主色调，传达温暖、活力的品牌形象：

- 主要操作按钮：`$primary-600` (#ff6d00)
- 强调元素：`$primary-500` (#ff860a)
- 辅助元素：`$primary-400` (#ffa232)
- 背景色：`$primary-100` (#fff1d3)
- 浅色强调：`$primary-50` (#fff9ec)

### 文本颜色层级

文本颜色应根据内容重要性分层使用：

- 主要文本：`$text-main` (#333)
- 次要文本：`$text-info` (#666666)
- 辅助文本：`$text-grey` (#999)
- 占位文本：`$text-placeholder` (#808080)
- 禁用文本：`$text-disable` (#c0c0c0)

### 背景色

- 页面背景：#f5f5f5
- 内容区块背景：#ffffff
- 悬停状态背景：#f1f1f1

## 排版规范

### 字号系统

- 大标题：36rpx，字重600
- 标题：32rpx，字重500
- 副标题：28rpx，字重500
- 正文：26rpx，字重400
- 辅助文本：24rpx，字重400
- 小文本：22rpx，字重400

### 行高规范

- 标题行高：1.4
- 正文行高：1.6
- 多行文本行高：1.8

## 间距规范

统一使用8的倍数作为间距基准（rpx单位）：

- 页面边距：左右20rpx
- 区块间距：20rpx
- 内容内边距：20rpx
- 列表项间距：20rpx
- 元素内间距：小(16rpx)、中(20rpx)、大(24rpx)

## 圆角规范

- 小圆角：4rpx
- 中圆角：8rpx
- 大圆角：16rpx
- 全圆角：9999rpx (用于胶囊按钮)

## 交互设计规范

### 按钮设计

1. **主要按钮**
   - 背景色：主题色 `$primary-600`
   - 文字颜色：白色
   - 大小：高度88rpx，文字30rpx

2. **次要按钮**
   - 边框：1px 主题色 `$primary-600`
   - 背景色：透明或白色
   - 文字颜色：主题色

3. **文本按钮**
   - 无边框和背景
   - 文字颜色：主题色
   - 文字大小：28rpx

### 输入框设计

- 高度：88rpx
- 内边距：左右20rpx
- 背景色：#F8F8F8
- 文字颜色：$text-main
- 占位符颜色：$text-placeholder

### 反馈设计

- 交互反馈时长：150ms-250ms
- 加载状态：使用主题色的加载动画
- 成功状态：绿色图标或提示
- 错误状态：红色图标或提示

## 列表设计

- 列表项高度：适中且一致
- 列表项间距：20rpx
- 图片尺寸：正方形或16:9比例
- 分割线：1px #EEEEEE

## 导航设计

- Tab栏高度：100rpx
- 选中状态：主题色下划线或图标填充
- 图标大小：48rpx
- 文字大小：24rpx

## 骨架屏规范

在数据加载时显示骨架屏，布局与实际内容一致：

- 骨架色：#EEEEEE
- 高亮色：线性渐变
- 动画：波浪从左到右

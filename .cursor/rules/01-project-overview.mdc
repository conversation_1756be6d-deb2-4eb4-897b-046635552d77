---
description: 
globs: 
alwaysApply: true
---
# 项目概述

这是一个基于uni-app构建的本地生活跨平台小程序项目，名称为"fnbdb-mini"。项目使用Vue 3、TypeScript和Vite构建，旨在提供本地生活服务和信息平台。

## 主要功能模块

项目包含四个主要功能模块：

1. **推荐** - 首页推荐内容，包含多个小模块：
   - 今日必抢
   - 附近抢购
   - 店铺推荐
   - 各类本地服务

2. **招聘求职** - 本地招聘信息和求职服务
   - 职位发布
   - 企业认证
   - 职位管理

3. **租房找房** - 房屋租赁信息平台

4. **相亲交友** - 社交平台功能

## 关键文件

- [src/main.ts](mdc:src/main.ts) - 项目入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/pages.json](mdc:src/pages.json) - 页面路由配置
- [src/manifest.json](mdc:src/manifest.json) - 应用配置文件
- [src/uni.scss](mdc:src/uni.scss) - 全局样式变量

## 技术栈

- 框架：uni-app、Vue 3、TypeScript
- 状态管理：Pinia
- UI工具：UnoCSS、@dcloudio/uni-ui、ThorUI组件
- 构建工具：Vite
- 包管理：PNPM
- 样式：SCSS

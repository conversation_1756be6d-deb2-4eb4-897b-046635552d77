---
description: 
globs: 
alwaysApply: true
---
# 开发指南

## 开发命令

主要开发命令（在`package.json`中定义）：

- `pnpm dev:h5` - 开发H5版本
- `pnpm dev:mp-weixin` - 开发微信小程序版本
- `pnpm dev:mp-alipay` - 开发支付宝小程序版本
- `pnpm build:h5` - 构建H5版本
- `pnpm build:mp-weixin` - 构建微信小程序版本
- `pnpm type-check` - 类型检查

## 项目配置

- [vite.config.ts](mdc:vite.config.ts) - Vite配置文件
- [uno.config.ts](mdc:uno.config.ts) - UnoCSS配置文件
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置文件
- [src/pages.json](mdc:src/pages.json) - 页面路由和导航配置

## 自动引入配置

项目配置了Vue和uni-app API自动引入，无需手动导入即可使用：

```typescript
// 不需要以下导入
// import { ref, computed } from 'vue'
// import { onLoad, onShow } from '@dcloudio/uni-app'

// 可直接使用
const count = ref(0)
const double = computed(() => count.value * 2)

onLoad(() => {
  console.log('页面加载')
})
```

## 工具函数

主要工具函数在`src/utils/`目录下：

- [utils/http.ts](mdc:src/utils/http.ts) - 网络请求工具，基于@uni-helper/uni-network
- [utils/date.ts](mdc:src/utils/date.ts) - 日期处理工具
- [utils/jobData.ts](mdc:src/utils/jobData.ts) - 工作数据相关工具

## 状态管理

使用Pinia进行状态管理：

- [stores/index.ts](mdc:src/stores/index.ts) - Pinia存储配置，包含持久化配置
- [stores/user.ts](mdc:src/stores/user.ts) - 用户状态管理

## 组件库使用

1. **ThorUI组件**

```vue
<template>
  <tui-tab 
    :tabs="tabs"
    :current="activeTabIndex"
    @change="handleTabChange"
  ></tui-tab>
</template>

<script setup>
import tuiTab from "@/components/thorui/tui-tab/tui-tab.vue";
</script>
```

2. **UnoCSS原子化CSS**

可直接在模板中使用UnoCSS提供的原子化类：

```vue
<view class="flex items-center justify-between p-4 bg-white rounded-lg shadow">
  <text class="text-lg font-bold text-gray-800">标题</text>
  <view class="ml-2 text-sm text-gray-500">详情</view>
</view>
```

## 页面开发流程

1. 在`src/pages.json`中配置页面路由
2. 在`src/pages/`目录下创建页面文件
3. 使用组件和工具函数构建页面
4. 测试不同平台的兼容性

## 代码质量控制

- 使用TypeScript进行类型检查
- 组件代码量控制在500行以内
- 避免重复代码，提取可复用逻辑
- 遵循样式指南和组件开发规范

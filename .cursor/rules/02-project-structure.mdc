---
description: 
globs: 
alwaysApply: true
---
# 项目结构

## 主要目录结构

- `src/` - 源代码目录
  - `components/` - 组件目录，包含可复用的UI组件
  - `pages/` - 页面目录，包含应用的各个页面
  - `static/` - 静态资源目录，存放图片、图标等
  - `stores/` - Pinia状态管理目录
  - `utils/` - 工具函数目录

## 页面模块结构

项目分为以下主要功能模块，按模块组织在`src/pages/`目录下：

### 1. 首页模块 (`home/`)
- `home.vue` - 首页主组件，包含四个主要标签页
- `components/` - 首页专用组件

### 2. 推荐内容 (`index/`)
- 包含推荐内容的索引页面
- 展示本地生活服务汇总

### 3. 工作模块 (`job/`)
- `detail.vue` - 工作详情页
- `publish.vue` - 发布工作信息页
- `company-auth.vue` - 企业认证页面
- `manage.vue` - 工作管理页面

### 4. 消息模块 (`message/`)
- 包含消息列表和聊天功能页面

### 5. 个人中心 (`mine/`)
- 用户个人信息和设置页面

### 6. 发布内容 (`post/`)
- 内容发布相关页面

## 组件结构

组件按用途分为以下几类：

### 1. 公共组件 (`components/`)
- `PostItem.vue` - 帖子项组件，展示帖子或信息卡片
- `ImageLoader.vue` - 图片加载和处理组件

### 2. 通用组件 (`components/common/`)
- 可在多个页面复用的基础组件

### 3. 首页专用组件 (`components/home/<USER>
- `RecommendContent.vue` - 推荐内容组件
- `JobContent.vue` - 招聘求职内容组件
- `HouseContent.vue` - 租房找房内容组件
- `DatingContent.vue` - 相亲交友内容组件
- `FilterOption.vue` - 筛选选项组件

### 4. 标签页组件 (`components/tabs/`)
- 标签页导航相关组件

### 5. ThorUI组件 (`components/thorui/`)
- 基于ThorUI库的自定义组件封装

## 静态资源结构

`static/`目录存放静态资源，建议按以下结构组织：

- `images/` - 图片资源
- `icons/` - 图标资源
- `fonts/` - 字体文件

## 工具函数组织

`utils/`目录包含以下工具函数：

- `http.ts` - 网络请求封装
- `date.ts` - 日期处理工具
- `jobData.ts` - 工作数据处理相关工具
- `index.ts` - 通用工具函数导出

## 状态管理结构

`stores/`目录使用Pinia管理应用状态：

- `index.ts` - Pinia配置，包含持久化存储设置
- `user.ts` - 用户状态管理

## 文件命名规范

- 组件文件：使用PascalCase，如`PostItem.vue`
- 工具函数：使用camelCase，如`httpUtils.ts`
- 页面文件：使用kebab-case，如`company-auth.vue`
- 样式文件：与对应组件同名，如`PostItem.scss`

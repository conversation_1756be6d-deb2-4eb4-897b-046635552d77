---
description: 
globs: 
alwaysApply: true
---
# 性能优化指南

## 代码优化

### 避免冗余代码

1. **组件复用**
   - 将重复使用的UI结构抽象为组件
   - 使用插槽(slot)增强组件灵活性
   - 通过props配置组件，避免硬编码

2. **逻辑复用**
   - 将通用逻辑封装为Composition Functions
   - 将业务逻辑封装到Pinia store中
   - 提取工具函数到`utils/`目录

3. **条件渲染**
   - 合理使用`v-if`与`v-show`
   - `v-if`：元素完全不需要渲染时使用
   - `v-show`：频繁切换显示状态时使用

### 减少组件体积

1. **组件拆分**
   - 单个组件代码控制在500行以内
   - 大型页面拆分为多个小组件
   - 按功能模块组织组件

2. **SCSS优化**
   - 利用嵌套语法减少冗余
   - 使用变量和混入(mixins)
   - 避免深层嵌套(不超过3层)

## 渲染性能

### 列表优化

1. **虚拟列表**
   - 对于长列表使用虚拟滚动
   - 使用项目中的`z-paging`组件实现分页和虚拟滚动

   ```vue
   <z-paging ref="paging" v-model="dataList" @query="queryList">
     <view v-for="(item, index) in dataList" :key="index">
       <!-- 列表项内容 -->
     </view>
   </z-paging>
   ```

2. **懒加载图片**
   - 使用`ImageLoader`组件懒加载图片
   - 设置适当的图片尺寸和质量
   - 使用webp格式减小图片体积

3. **合理使用key**
   - 始终为`v-for`设置唯一key
   - 避免使用索引作为key(除非列表不会变化)

### 计算属性与侦听器

1. **计算属性优化**
   - 合理使用计算属性缓存结果
   - 避免在计算属性中执行复杂操作或产生副作用

2. **侦听器使用**
   - 使用immediate选项立即触发侦听
   - 使用deep选项侦听深层变化
   - 合理设置debounce防抖

## 网络请求优化

1. **数据缓存**
   - 缓存不常变化的数据
   - 使用Pinia的持久化存储

2. **请求合并**
   - 合并多个相关API请求
   - 使用批量接口减少请求次数

3. **按需加载**
   - 实现数据分页加载
   - 图片和资源懒加载

## 小程序特定优化

1. **分包加载**
   - 将不同功能拆分为分包
   - 主包只包含首页和必要组件

2. **预加载**
   - 提前加载下个可能的页面
   ```js
   // 页面 onLoad 中
   uni.preloadPage({
     url: '/pages/detail/detail'
   });
   ```

3. **setData优化**
   - 避免频繁setData
   - 合并多次数据更新
   - 避免传递大量数据

## 启动优化

1. **首屏渲染**
   - 减少首屏加载的组件数量
   - 使用骨架屏提升体验
   - 优先加载关键路径内容

2. **资源预加载**
   - 优先加载核心资源
   - 延迟加载非关键资源

## 监控与分析

1. **性能监控**
   - 记录页面加载时间
   - 监控内存使用情况
   - 分析组件渲染性能

2. **优化方法**
   - 定位并解决性能瓶颈
   - 持续迭代优化
   - 定期检查代码质量

---
description: 
globs: 
alwaysApply: true
---
# 组件开发与复用规范

## 组件设计原则

1. **单一职责**
   - 每个组件应专注于完成一个特定功能
   - 避免组件功能过于复杂或承担多个不相关的职责

2. **可复用性**
   - 设计组件时考虑通用性
   - 抽象出可配置的属性，使组件能适应不同场景
   - 通过Props传递参数，而不是在组件内部硬编码

3. **可维护性**
   - 组件代码量控制在500行以内
   - 组件结构清晰，包含必要的注释
   - 复杂组件拆分为多个小组件

## 组件目录结构

```
src/components/
  ├── common/       # 通用组件
  ├── home/         # 首页相关组件
  ├── tabs/         # 标签页组件
  ├── thorui/       # ThorUI组件库的自定义组件
  ├── PostItem.vue  # 帖子项组件
  └── ImageLoader.vue # 图片加载组件
```

## 组件命名规范

- 使用**PascalCase**命名组件文件和组件名
- 文件名与组件名保持一致
- 基础组件使用`Base`前缀，如`BaseButton.vue`
- 特定业务组件使用业务模块前缀，如`JobCard.vue`

## 组件编写规范

1. **模板结构**
   ```vue
   <template>
     <view class="component-name">
       <!-- 组件内容 -->
     </view>
   </template>
   ```

2. **Script部分**
   ```vue
   <script setup lang="ts">
   // 1. 导入依赖
   import { ref, computed } from 'vue';
   import type { PropType } from 'vue';
   
   // 2. 定义Props
   const props = defineProps({
     title: {
       type: String,
       required: true,
     },
     // 其他属性...
   });
   
   // 3. Emits定义
   const emit = defineEmits(['update', 'click']);
   
   // 4. 响应式数据
   const localData = ref('');
   
   // 5. 计算属性
   const computedValue = computed(() => {
     return props.title.toUpperCase();
   });
   
   // 6. 方法
   const handleClick = () => {
     emit('click');
   };
   </script>
   ```

3. **Style部分**
   ```vue
   <style lang="scss" scoped>
   .component-name {
     // 组件样式，使用嵌套语法
     &__element {
       // 元素样式
     }
   }
   </style>
   ```

## 组件复用策略

1. **抽象通用逻辑**
   - 使用Composition API封装可复用的逻辑
   - 创建自定义Hooks存放在`src/hooks/`目录下

2. **业务组件分层**
   - 基础组件：纯展示型组件，无业务逻辑
   - 容器组件：包含业务逻辑和状态管理
   - 页面组件：组合多个容器组件和基础组件

3. **避免代码冗余**
   - 不同模块的相似组件应抽象为通用组件
   - 通过配置化参数实现个性化需求
   - 使用插槽(slots)增强组件的灵活性

4. **性能优化**
   - 合理使用`v-if`和`v-show`
   - 列表渲染使用唯一key
   - 避免不必要的计算和渲染
   - 

5. **自动引入**
   - vite中已经引入了`unplugin-auto-import/vite`组件，所以"vue", "uni-app"内置的方法可以不需要引入就能直接使用
